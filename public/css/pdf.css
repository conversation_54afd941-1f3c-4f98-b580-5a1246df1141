body {
  font-family: "Open Sans", sans-serif;
  margin: 0;
  padding: 0;
}

/** HEADER **/
.header {
  display: flex;
}
.header .header-title, .header .header-date {
  width: 50%;
}
.header .title {
  font-size: 24px;
  line-height: 32px;
  color: #000000;
  text-transform: uppercase;
  position: relative;
  font-weight: 500;
  padding-left: 30px;
  padding-right: 20px;
}
.header .title:before {
  content: "";
  position: absolute;
  left: -8px;
  width: 20px;
  height: 100%;
  background-color: var(--color1);
}
.header .header-date {
  display: flex;
  position: relative;
}
.header .header-date .date-ctn {
  background-color: #D0D3D4;
  width: 50%;
  display: flex;
  margin-left: 2px;
  align-items: center;
  justify-content: center;
}
.header .header-date .date-ctn:first-child {
  margin-right: 2px;
  margin-left: 0;
}
.header .header-date .date-ctn span {
  font-family: "Open Sans";
  font-weight: 700;
  font-size: 15px;
  line-height: 24px;
  text-align: center;
}
.header .header-date .icon-direction {
  width: 30px;
  height: 30px;
  background-color: white;
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
  background-repeat: no-repeat;
  background-size: 13px;
  background-position: center;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -15px;
  margin-left: -15px;
}

/** Property Ctn **/
.property-ctn {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
}
.property-ctn .property-info {
  width: 50%;
}
.property-ctn .property-info .site-logo {
  padding-right: 40px;
}
.property-ctn .property-info .site-logo .inner {
  padding: 15px 20px;
  border-bottom: solid 1px #dbdcdd;
}
.property-ctn .property-info .site-logo img {
  max-width: 200px;
  max-height: 70px;
  display: block;
}
.property-ctn .property-info .brokers-list {
  padding: 20px 20px 20px 20px;
}
.property-ctn .property-info .brokers-list .broker {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 20px;
}
.property-ctn .property-info .brokers-list .broker:last-child {
  margin-bottom: 0;
}
.property-ctn .property-info .brokers-list .broker .broker-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}
.property-ctn .property-info .brokers-list .broker .broker-img img {
  width: 100%;
}
.property-ctn .property-info .brokers-list .broker .broker-info {
  padding-left: 30px;
}
.property-ctn .property-info .brokers-list .broker .broker-info .name {
  display: block;
  font-weight: 600;
  font-size: 16px;
  line-height: 23px;
}
.property-ctn .property-info .brokers-list .broker .broker-info .role {
  display: block;
  font-size: 11px;
  line-height: 16px;
  text-transform: uppercase;
  color: #666666;
  margin: 5px 0;
  width: 150px;
  word-wrap: break-word;
  white-space: initial;
}
.property-ctn .property-info .brokers-list .broker .broker-info .phone {
  font-size: 13px;
  line-height: 22px;
  font-weight: 600;
  position: relative;
  padding-left: 25px;
  color: black;
  text-decoration: none;
}
.property-ctn .property-info .brokers-list .broker .broker-info .phone:before {
  content: "";
  position: absolute;
  height: 20px;
  width: 14px;
  left: 0;
  top: -1px;
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
  background-repeat: no-repeat;
  background-size: 11px;
  background-position: center;
}
.property-ctn .property-img {
  width: 50%;
}
.property-ctn .property-img .image {
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
  height: 200px;
}
.property-ctn .property-img .inner {
  display: flex;
}
.property-ctn .property-img .inner .address-ctn {
  padding: 20px 0px 20px 0px;
  width: 71%;
  background-color: var(--color3);
}
.property-ctn .property-img .inner .address-ctn .address {
  margin: 0;
  color: white;
  font-size: 18px;
  line-height: 27px;
  padding: 0 20px;
}
.property-ctn .property-img .inner .address-ctn .municipality {
  margin: 0;
  font-size: 12px;
  line-height: 18px;
  color: white;
  padding: 0 20px;
}
.property-ctn .property-img .inner .price-ctn {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color2);
}
.property-ctn .property-img .inner .price-ctn .price {
  font-size: 18px;
  line-height: 22px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/** Visit Data CTN **/
.visit-data-ctn {
  display: flex;
  flex-wrap: wrap;
}
.visit-data-ctn .number-ctn {
  width: 30%;
}
.visit-data-ctn .number-ctn .number-item {
  height: 270px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.visit-data-ctn .number-ctn .number-item.-red {
  background-color: var(--color1);
}
.visit-data-ctn .number-ctn .number-item.-blue {
  background-color: var(--color2);
}
.visit-data-ctn .number-ctn .number-item.-dark-blue {
  background-color: var(--color3);
}
.visit-data-ctn .number-ctn .number-item .inner {
  position: relative;
  padding-top: 30px;
}
.visit-data-ctn .number-ctn .number-item .inner .icon {
  width: 30px;
  height: 30px;
  content: "";
  background-repeat: no-repeat;
  background-size: 30px;
  background-position: center;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -15px;
}
.visit-data-ctn .number-ctn .number-item .inner .icon.-visitors {
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
}
.visit-data-ctn .number-ctn .number-item .inner .icon.-calendar {
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
}
.visit-data-ctn .number-ctn .number-item .inner .icon.-time {
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
}
.visit-data-ctn .number-ctn .number-item .inner .number-data {
  color: white;
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  margin: 10px 0;
}
.visit-data-ctn .number-ctn .number-item .inner .number-label {
  font-size: 14px;
  line-height: 20px;
  color: #FFFFFF;
  margin: 0;
}
.visit-data-ctn .number-ctn .number-item .inner .number {
  color: #FFFFFF;
}
.visit-data-ctn .graph-ctn {
  width: 70%;
}
.visit-data-ctn .graph-ctn .visitors-data {
  background-color: #F2F4F4;
  display: flex;
  align-items: center;
  padding: 0 30px;
  height: 270px;
}
.visit-data-ctn .graph-ctn .visitors-data .inner {
  width: 100%;
  display: flex;
  align-items: center;
}
.visit-data-ctn .graph-ctn .visitors-data .inner .visitors-data-caption {
  width: 50%;
}
.visit-data-ctn .graph-ctn .visitors-data .inner .visitors-data-caption .list-stats-ctn {
  margin-right: 15px;
}
.visit-data-ctn .graph-ctn .visitors-data .inner .doughnut-ctn {
  margin-left: 45px;
}
.visit-data-ctn .graph-ctn .visitors-data .inner .doughnut-ctn .doughnut-chart {
  margin: 0 auto;
}
.visit-data-ctn .graph-ctn .visitors-data-number {
  background-color: #D0D3D4;
  padding: 0 30px;
  height: 270px;
  display: flex;
  align-items: center;
}
.visit-data-ctn .graph-ctn .visitors-data-number .inner {
  width: 100%;
  padding: 15px 0;
}
.visit-data-ctn .graph-ctn .visitors-data-number .visitors-data-number-caption {
  display: flex;
}
.visit-data-ctn .graph-ctn .visitors-data-number .visitors-data-number-caption .list-stats-ctn li .label {
  max-width: 180px;
}
.visit-data-ctn .graph-ctn .visitors-data-number .visitors-data-number-caption .list-stats-ctn li:last-child {
  border-bottom: none;
}

/** Number Visits Chart CTN **/
.visits-chart-ctn .line-chart-ctn {
  margin-top: 40px;
  text-align: center;
}

/** Geograph data CTN **/
.geograph-data-ctn {
  background-color: #F2F4F4;
  display: flex;
  align-items: center;
  padding: 0 30px;
  height: 300px;
  margin: 30px 0 0;
}
.geograph-data-ctn .inner {
  width: 100%;
  padding: 15px 0;
  display: flex;
  align-items: center;
}
.geograph-data-ctn .inner .geograph-data-caption {
  width: 50%;
}
.geograph-data-ctn .inner .doughnut-ctn {
  text-align: center;
  margin-left: 100px;
}
.geograph-data-ctn .inner .doughnut-ctn .doughnut-chart {
  margin: 0 auto;
}

/** Others data CTN **/
.others-data-ctn {
  display: flex;
}
.others-data-ctn .device-data-ctn {
  width: 50%;
  background-color: var(--color3);
}
.others-data-ctn .device-data-ctn .inner {
  padding: 30px 40px;
}
.others-data-ctn .device-data-ctn .inner .device-list {
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  list-style: none;
  padding-top: 20px;
}
.others-data-ctn .device-data-ctn .inner .device-list li {
  text-align: center;
  position: relative;
  padding-top: 30px;
  width: 33%;
}
.others-data-ctn .device-data-ctn .inner .device-list li .icon {
  width: 30px;
  height: 30px;
  content: "";
  background-repeat: no-repeat;
  background-size: 30px;
  background-position: center;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -15px;
}
.others-data-ctn .device-data-ctn .inner .device-list li .icon.-desktop {
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
}
.others-data-ctn .device-data-ctn .inner .device-list li .icon.-tablet {
  width: 26px;
  height: 30px;
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
  background-size: 26px;
  margin-left: -13px;
}
.others-data-ctn .device-data-ctn .inner .device-list li .icon.-mobile {
  width: 20px;
  height: 30px;
  background-image: url("http://v3.e-closion.ca/images/<EMAIL>");
  background-size: 20px;
  margin-left: -10px;
}
.others-data-ctn .device-data-ctn .inner .device-list li .data {
  color: white;
  font-size: 20px;
  line-height: 28px;
  font-weight: 700;
  margin: 10px 0;
}
.others-data-ctn .device-data-ctn .inner .device-list li .label {
  font-size: 14px;
  line-height: 20px;
  color: #fff;
  margin: 0;
}
.others-data-ctn .lang-data-ctn {
  width: 50%;
  background-color: var(--color2);
}
.others-data-ctn .lang-data-ctn .inner {
  padding: 30px 40px;
}
.others-data-ctn .lang-data-ctn .inner .list-stats-ctn {
  padding-top: 20px;
}
.others-data-ctn .lang-data-ctn .inner .list-stats-ctn li {
  padding-left: 0px;
  border-bottom: solid 1px #39416F;
}
.others-data-ctn .lang-data-ctn .inner .list-stats-ctn li .data {
  color: white;
}
.others-data-ctn .lang-data-ctn .inner .list-stats-ctn li .label {
  color: white;
}
.others-data-ctn .lang-data-ctn .inner .list-stats-ctn li:before {
  content: none;
}

.data-bloc .title-emphase {
  font-size: 16px;
  line-height: 22px;
  color: black;
  text-transform: uppercase;
  padding-left: 20px;
  position: relative;
  margin-top: 0;
  margin-bottom: 20px;
}
.data-bloc .title-emphase:before {
  content: "";
  position: absolute;
  width: 5px;
  height: 100%;
  background-color: var(--color1);
  left: 0;
  top: 0;
}
.data-bloc .title-emphase.-white {
  color: white;
}
.data-bloc .title-emphase.-white:before {
  background-color: white;
}
.data-bloc .list-stats-ctn {
  padding: 0;
  list-style: none;
  margin: 0;
}
.data-bloc .list-stats-ctn li {
  padding-left: 30px;
  padding-right: 40px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: solid 1px #dbdede;
  position: relative;
  display: flex;
  justify-content: space-between;
}
.data-bloc .list-stats-ctn li:before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid #FFFFFF;
  background-color: var(--color1);
  border-radius: 50%;
  left: 0;
  top: 3px;
}
.data-bloc .list-stats-ctn li:nth-child(2):before {
  background-color: var(--color2);
}
.data-bloc .list-stats-ctn li:nth-child(3):before {
  background-color: var(--color3);
}
.data-bloc .list-stats-ctn li:nth-child(4):before {
  background-color: #ADADAD !important;
}
.data-bloc .list-stats-ctn li .data {
  color: #000000;
  font-size: 15px;
  font-weight: 700;
  line-height: 20px;
  position: absolute;
  right: 0;
  top: 1px;
}
.data-bloc .list-stats-ctn li .label {
  color: #333333;
  font-size: 13px;
  line-height: 20px;
  padding-right: 20px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 150px;
  display: block;
}
.data-bloc .list-stats-ctn.-flex {
  width: 50%;
}
.data-bloc .list-stats-ctn.-flex li {
  margin-right: 15px;
  padding-left: 0;
}
.data-bloc .list-stats-ctn.-flex li:before {
  content: none;
}
.data-bloc .list-stats-ctn.-flex.-no-margin li {
  margin-right: 0;
  margin-left: 15px;
}
