@import url(https://fonts.googleapis.com/css?family=Raleway:300,400,600);
@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,600,700);
@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,600,700);
/***** FONTS ******/
/***** Colors ******/
/***** Transition ******/
/***** Breakpoints ******/
/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Change the default font family in all browsers (opinionated).
 * 2. Correct the line height in all browsers.
 * 3. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 */
html {
  font-family: "Open Sans", sans-serif; /* 1 */
  line-height: 1.15; /* 2 */
  -ms-text-size-adjust: 100%; /* 3 */
  -webkit-text-size-adjust: 100%; /* 3 */
}

*, *:after, *:before {
  box-sizing: border-box;
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers (opinionated).
 */
body {
  margin: 0;
}

/**
 * Add the correct display in IE 9-.
 */
article,
aside,
footer,
header,
nav,
section {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in IE.
 */
figcaption,
figure,
main { /* 1 */
  display: block;
}

/**
 * Add the correct margin in IE 8.
 */
figure {
  margin: 1em 40px;
}

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
  background-color: transparent; /* 1 */
  -webkit-text-decoration-skip: objects; /* 2 */
  text-decoration: none;
}

/**
 * Remove the outline on focused links when they are also active or hovered
 * in all browsers (opinionated).
 */
a:active,
a:hover,
a:focus {
  outline-width: 0;
  text-decoration: none;
}

ul {
  list-style: none;
}

/**
 * 1. Remove the bottom border in Firefox 39-.
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted; /* 2 */
}

/**
 * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
 */
b,
strong {
  font-weight: inherit;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font style in Android 4.3-.
 */
dfn {
  font-style: italic;
}

/**
 * Add the correct background and color in IE 9-.
 */
mark {
  background-color: #ff0;
  color: #000;
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 */
audio,
video {
  display: inline-block;
}

/**
 * Add the correct display in iOS 4-7.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Remove the border on images inside links in IE 10-.
 */
img {
  border-style: none;
}

/**
 * Hide the overflow in IE.
 */
svg:not(:root) {
  overflow: hidden;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers (opinionated).
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: "Open Sans", sans-serif; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input { /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select { /* 1 */
  text-transform: none;
}

/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */
button,
html [type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button; /* 2 */
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Change the border, margin, and padding in all browsers (opinionated).
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * 1. Add the correct display in IE 9-.
 * 2. Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}

/**
 * Remove the default vertical scrollbar in IE.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */
[type=checkbox],
[type=radio] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 */
details,
menu {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Scripting
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 */
canvas {
  display: inline-block;
}

/**
 * Add the correct display in IE.
 */
template {
  display: none;
}

/* Hidden
   ========================================================================== */
/**
 * Add the correct display in IE 10-.
 */
[hidden] {
  display: none;
}

/*!
 * Bootstrap Grid v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
html {
  box-sizing: border-box;
  -ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1160px;
  }
}
.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*=col-] {
  padding-right: 0;
  padding-left: 0;
}

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,
.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,
.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,
.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: none;
}

.col-1 {
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.col-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-11 {
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  order: -1;
}

.order-last {
  order: 13;
}

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.offset-1 {
  margin-left: 8.333333%;
}

.offset-2 {
  margin-left: 16.666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.333333%;
}

.offset-5 {
  margin-left: 41.666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.333333%;
}

.offset-8 {
  margin-left: 66.666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.333333%;
}

.offset-11 {
  margin-left: 91.666667%;
}

@media (min-width: 576px) {
  .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-sm-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-sm-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-sm-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-sm-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-sm-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    order: -1;
  }
  .order-sm-last {
    order: 13;
  }
  .order-sm-0 {
    order: 0;
  }
  .order-sm-1 {
    order: 1;
  }
  .order-sm-2 {
    order: 2;
  }
  .order-sm-3 {
    order: 3;
  }
  .order-sm-4 {
    order: 4;
  }
  .order-sm-5 {
    order: 5;
  }
  .order-sm-6 {
    order: 6;
  }
  .order-sm-7 {
    order: 7;
  }
  .order-sm-8 {
    order: 8;
  }
  .order-sm-9 {
    order: 9;
  }
  .order-sm-10 {
    order: 10;
  }
  .order-sm-11 {
    order: 11;
  }
  .order-sm-12 {
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.333333%;
  }
  .offset-sm-2 {
    margin-left: 16.666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.333333%;
  }
  .offset-sm-5 {
    margin-left: 41.666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.333333%;
  }
  .offset-sm-8 {
    margin-left: 66.666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.333333%;
  }
  .offset-sm-11 {
    margin-left: 91.666667%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-md-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-md-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-md-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    order: -1;
  }
  .order-md-last {
    order: 13;
  }
  .order-md-0 {
    order: 0;
  }
  .order-md-1 {
    order: 1;
  }
  .order-md-2 {
    order: 2;
  }
  .order-md-3 {
    order: 3;
  }
  .order-md-4 {
    order: 4;
  }
  .order-md-5 {
    order: 5;
  }
  .order-md-6 {
    order: 6;
  }
  .order-md-7 {
    order: 7;
  }
  .order-md-8 {
    order: 8;
  }
  .order-md-9 {
    order: 9;
  }
  .order-md-10 {
    order: 10;
  }
  .order-md-11 {
    order: 11;
  }
  .order-md-12 {
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.333333%;
  }
  .offset-md-2 {
    margin-left: 16.666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.333333%;
  }
  .offset-md-5 {
    margin-left: 41.666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.333333%;
  }
  .offset-md-8 {
    margin-left: 66.666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.333333%;
  }
  .offset-md-11 {
    margin-left: 91.666667%;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-lg-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-lg-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-lg-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-lg-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    order: -1;
  }
  .order-lg-last {
    order: 13;
  }
  .order-lg-0 {
    order: 0;
  }
  .order-lg-1 {
    order: 1;
  }
  .order-lg-2 {
    order: 2;
  }
  .order-lg-3 {
    order: 3;
  }
  .order-lg-4 {
    order: 4;
  }
  .order-lg-5 {
    order: 5;
  }
  .order-lg-6 {
    order: 6;
  }
  .order-lg-7 {
    order: 7;
  }
  .order-lg-8 {
    order: 8;
  }
  .order-lg-9 {
    order: 9;
  }
  .order-lg-10 {
    order: 10;
  }
  .order-lg-11 {
    order: 11;
  }
  .order-lg-12 {
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.333333%;
  }
  .offset-lg-2 {
    margin-left: 16.666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.333333%;
  }
  .offset-lg-5 {
    margin-left: 41.666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.333333%;
  }
  .offset-lg-8 {
    margin-left: 66.666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.333333%;
  }
  .offset-lg-11 {
    margin-left: 91.666667%;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }
  .col-xl-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .col-xl-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .col-xl-5 {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .col-xl-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .col-xl-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    order: -1;
  }
  .order-xl-last {
    order: 13;
  }
  .order-xl-0 {
    order: 0;
  }
  .order-xl-1 {
    order: 1;
  }
  .order-xl-2 {
    order: 2;
  }
  .order-xl-3 {
    order: 3;
  }
  .order-xl-4 {
    order: 4;
  }
  .order-xl-5 {
    order: 5;
  }
  .order-xl-6 {
    order: 6;
  }
  .order-xl-7 {
    order: 7;
  }
  .order-xl-8 {
    order: 8;
  }
  .order-xl-9 {
    order: 9;
  }
  .order-xl-10 {
    order: 10;
  }
  .order-xl-11 {
    order: 11;
  }
  .order-xl-12 {
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.333333%;
  }
  .offset-xl-2 {
    margin-left: 16.666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.333333%;
  }
  .offset-xl-5 {
    margin-left: 41.666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.333333%;
  }
  .offset-xl-8 {
    margin-left: 66.666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.333333%;
  }
  .offset-xl-11 {
    margin-left: 91.666667%;
  }
}
.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
}
@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
}
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 768px) {
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 992px) {
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
}
@media (min-width: 1200px) {
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
}

body, body.skin-green {
  position: relative;
  background-color: #D7E0E6;
  color: #3D3F43;
}

a {
  transition: all 0.4s cubic-bezier(0.55, 0.22, 0.36, 0.9);
  text-decoration: none !important;
}

hr {
  color: #6B7076;
}

.hidden {
  display: none !important;
}

.btn {
  display: flex !important;
  align-items: center;
  box-sizing: border-box;
  width: -moz-max-content;
  width: max-content;
  height: 35px;
  min-height: 35px;
  padding: 0;
  border-radius: 5px;
  border: 0 none;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.55, 0.22, 0.36, 0.9);
  font-family: "Open Sans", sans-serif;
  padding: 0 18px 2px;
  box-shadow: 0 4px 6px 0 rgba(50, 50, 93, 0.11), 0 1px 3px 0 rgba(0, 0, 0, 0.08);
}
.btn i {
  transition: all 0.4s cubic-bezier(0.55, 0.22, 0.36, 0.9);
}
.btn.btn-default {
  color: #6B7076;
  background-color: #EEF0F2;
  border: 0 none;
  font-size: 13px;
  line-height: 22px;
  font-weight: 600;
}
.btn.btn-primary, .btn.btn-success {
  background-color: #008060 !important;
  color: white !important;
  border: none;
  font-size: 13px;
  line-height: 22px;
  font-weight: 600;
}
.btn.btn-primary:hover, .btn.btn-primary:focus, .btn.btn-primary:focus-visible, .btn.btn-primary:active, .btn.btn-success:hover, .btn.btn-success:focus, .btn.btn-success:focus-visible, .btn.btn-success:active {
  background-color: #005741 !important;
}
.btn.btn-outline-primary {
  color: #6B7076;
  background-color: #EEF0F2;
  border: none;
}
.btn.btn-outline-primary:hover {
  color: white;
  background-color: #005741;
}
.btn.inline-create-button {
  position: absolute;
  top: -5px;
  right: 15px;
  padding: 0;
  color: #008060;
}
.btn.inline-create-button span.la {
  display: block !important;
}
.btn.inline-create-button:hover {
  text-decoration: none;
  color: #005741;
}
.btn span.la, .btn.dropdown-toggle::after {
  display: none !important;
}

table td:last-child .btn {
  display: inline-block !important;
  width: 45px !important;
  max-width: 45px !important;
  height: 35px !important;
  min-height: 0;
  padding: 0;
  border: 1px solid rgba(133, 139, 145, 0.3);
  color: rgba(0, 0, 0, 0) !important;
  box-shadow: none;
  background: none;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
table td:last-child .btn i {
  width: 100%;
  color: #858B91;
  font-size: 22px;
  line-height: 34px;
}
table td:last-child .btn:hover {
  border-color: #008060;
  background-color: #008060;
}
table td:last-child .btn:hover i {
  color: white;
}
table td:last-child .btn.disabled {
  pointer-events: none !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
  /* Firefox */
}

.form-group {
  margin-bottom: 30px !important;
}
.form-group label {
  font-weight: 600;
}
.form-group .form-control[type=text],
.form-group .form-control[type=email],
.form-group .form-control[type=tel],
.form-group .form-control[type=url],
.form-group .form-control[type=number],
.form-group .form-control[type=password],
.form-group .form-control[type=date],
.form-group .form-control[type=datetime-local],
.form-group .select2-selection__rendered,
.form-group .backstrap-file,
.form-group .iconpicker,
.form-group textarea,
.form-group select {
  height: 45px;
  border-radius: 5px;
  border: 0 none;
  box-shadow: 0 1px 3px 0 rgba(50, 50, 93, 0.2), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  color: rgba(0, 0, 0, 0.6);
}
.form-group .backstrap-file {
  overflow: hidden;
}
.form-group .backstrap-file label {
  height: 100%;
  margin: 0;
  padding: 10px;
}
.form-group .backstrap-file label::after {
  height: 100%;
  padding: 10px;
}
.form-group select ~ span.select2.select2-container {
  display: block;
  height: 45px;
}
.form-group select ~ span.select2.select2-container .select2-selection.select2-selection--single {
  padding: 0;
  height: 100%;
  border: 0 none !important;
}
.form-group select ~ span.select2.select2-container .select2-selection.select2-selection--single .select2-selection__rendered {
  padding: 12px;
  line-height: 20px;
}
.form-group select ~ span.select2.select2-container .select2-selection__clear {
  position: relative;
  top: 0;
  right: 10px;
  font-size: 24px;
}
.form-group select ~ span.select2.select2-container .select2-selection__clear::after {
  display: none;
}
.form-group select ~ span.select2.select2-container .select2-selection__choice {
  height: 32px;
  padding: 10px !important;
  line-height: 10px;
}
.form-group select ~ span.select2.select2-container .select2-search__field {
  height: 100% !important;
  padding-top: 5px !important;
}
.form-group .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 10px;
  right: 10px;
}
.form-group .checkbox, .form-group .radio {
  position: relative;
  display: block;
  margin-top: 0px;
  margin-bottom: 12px;
}
.form-group .checkbox label, .form-group .radio label {
  padding: 0;
}
.form-group .checkbox input[type=checkbox], .form-group .checkbox input[type=radio], .form-group .radio input[type=checkbox], .form-group .radio input[type=radio] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 20px;
  height: 20px;
  position: relative;
  top: 4px;
  margin-right: 10px;
  margin-left: 0;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: white;
  box-shadow: 0 1px 3px 0 rgba(50, 50, 93, 0.2), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  transition: all 0.2s;
}
.form-group .checkbox input[type=checkbox]:checked, .form-group .checkbox input[type=radio]:checked, .form-group .radio input[type=checkbox]:checked, .form-group .radio input[type=radio]:checked {
  background-color: #008060;
  border-color: #008060;
}
.form-group .checkbox input[type=checkbox]:checked::after, .form-group .checkbox input[type=radio]:checked::after, .form-group .radio input[type=checkbox]:checked::after, .form-group .radio input[type=radio]:checked::after {
  content: url("../../images/icon/checked.svg");
  display: inline-block;
  position: absolute;
  left: 5px;
  top: -2px;
  transform: scale(1.2);
}
.form-group .checkbox input[type=radio], .form-group .radio input[type=radio] {
  border-radius: 50%;
}
.form-group .checkbox input[type=radio]:checked, .form-group .radio input[type=radio]:checked {
  background: none;
}
.form-group .checkbox input[type=radio]:checked::after, .form-group .radio input[type=radio]:checked::after {
  content: "";
  height: 12px;
  width: 12px;
  left: 3px;
  top: 3px;
  background-color: #008060;
  border-radius: 50%;
}
.form-group .note-frame {
  background-color: white !important;
  border-radius: 5px;
  box-shadow: 0 1px 3px 0 rgba(50, 50, 93, 0.2), 0 1px 0 0 rgba(0, 0, 0, 0.02);
}
.form-group .note-frame .note-toolbar .btn {
  height: auto;
  padding: 4px 10px;
  border-radius: 0;
  font-size: inherit;
  box-shadow: none;
  background: none;
  border: 0 none;
}
.form-group .video-previewSuffix {
  height: 100%;
  position: absolute;
  right: 0;
  z-index: 99;
}
.form-group .video-previewSuffix .video-previewLink, .form-group .video-previewSuffix .video-previewImage {
  width: 45px;
  height: 45px;
}
.form-group .video-previewSuffix .video-previewLink i.la, .form-group .video-previewSuffix .video-previewImage i.la {
  font-size: 30px;
  margin-top: 4px;
}
.form-group .container-repeatable-elements .repeatable-element {
  margin: 0 0 10px !important;
  padding: 12px 0 0 30px !important;
  border: 0 none;
  box-shadow: none;
  background-color: #D5D8DB;
}
.form-group .container-repeatable-elements .repeatable-element .controls {
  left: 7px;
  top: 40px;
}
.form-group .jumbotron.how-to-create {
  margin: 0;
  padding: 0;
}
.form-group .input-group-append .input-group-text {
  display: flex;
  justify-content: right;
  min-width: 100px;
  height: 100%;
  position: absolute;
  left: auto;
  right: 6px;
  top: 0;
  padding: 0;
  z-index: 999;
  border: 0 none;
  background: none;
  font-size: 14px;
  color: #AAA;
  text-align: right;
}
.form-group .input-group-append .input-group-text.colorpicker-input-addon {
  min-width: auto;
  top: 2px;
  left: -45px;
  border: none;
}
.form-group .input-group-append .input-group-text.colorpicker-input-addon i {
  height: 32px;
  width: 32px;
  border-radius: 5px;
}
.form-group .input-group-append .input-group-text .btn-copy {
  color: #3D3F43;
}
.form-group .input-group-append .input-group-text .btn-copy:hover {
  color: black;
}
.form-group .input-group {
  display: flex !important;
}
.form-group .input-group .form-control {
  float: none;
  width: auto;
  display: block;
}
.form-group .input-group .input-group-prepend .input-group-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  background-color: white;
  border: 0;
}
.form-group .iconpicker {
  width: 80px;
}
.form-group .iconpicker span.caret {
  position: relative;
  right: 0px;
}
.form-group .iconpicker i.custom {
  margin: auto;
  font-size: 19px;
}
.form-group .slidecontainer {
  padding-top: 10px;
}
.form-group .slidecontainer input[type=range] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #D9DEE4;
  border-radius: 15px;
  height: 10px;
  /* WebKit/Blink */
}
.form-group .slidecontainer input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  border: 0 none;
  height: 26px;
  width: 26px;
  border-radius: 50px;
  background: #008060;
  cursor: pointer;
  margin-top: -14px;
}
.form-group .slidecontainer input[type=range] {
  /* Firefox */
}
.form-group .slidecontainer input[type=range]::-moz-range-thumb {
  border: 0 none;
  height: 26px;
  width: 26px;
  border-radius: 50px;
  background: #008060;
  cursor: pointer;
}
.form-group .slidecontainer input[type=range] {
  /* IE */
}
.form-group .slidecontainer input[type=range]::-ms-thumb {
  border: 0 none;
  height: 26px;
  width: 26px;
  border-radius: 50px;
  background: #008060;
  cursor: pointer;
}
.form-group .slidecontainer .slider-controls {
  display: flex;
  justify-content: space-between;
  margin-top: 14px;
}
.form-group .note-editing-area ul {
  list-style-type: disc;
}

.iconpicker-popover .btn-icon:not(.btn-primary) {
  background: none;
  box-shadow: none;
}
.iconpicker-popover .btn-icon:not(.btn-primary):hover {
  background-color: #008060;
}
.iconpicker-popover .btn-icon:not(.btn-primary):hover i.custom {
  color: white;
}
.iconpicker-popover .btn-primary span {
  margin: auto;
}
.iconpicker-popover i.custom {
  margin: auto;
  font-size: 19px;
}

.xdsoft_datetimepicker {
  width: auto;
  top: 270px;
  left: 40px;
  padding: 28px 20px;
  border: 1px solid #CCCCCC;
  border-radius: 5px;
  box-shadow: none;
}
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_prev, .xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_next {
  visibility: hidden;
}
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_scroller_box {
  border-radius: 5px;
  height: 200px;
}
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time {
  background: none !important;
}
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time:hover {
  background: rgb(1, 90, 1) !important;
}
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_current {
  background: green !important;
}
.xdsoft_datetimepicker .xdsoft_datepicker {
  width: auto;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker {
  display: flex;
  justify-content: space-between;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker .xdsoft_next, .xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker .xdsoft_prev {
  background-color: #ff7ff9;
  border-radius: 50%;
  transform: scaleY(0.7);
  color: white;
  padding-bottom: 5px;
  filter: invert(100%);
  opacity: 1;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker .xdsoft_next:hover, .xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker .xdsoft_prev:hover {
  opacity: 0.7;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_monthpicker .xdsoft_label {
  width: auto;
  color: #2A2B2D;
  font-family: "Open Sans";
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 0.59px;
  line-height: 20px;
  text-align: center;
  text-transform: uppercase;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar th {
  background: none;
  border: 0px none;
  color: #2A2B2D;
  font-family: "Open Sans";
  font-size: 0;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 19.01px;
  text-align: center;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar th::first-letter {
  font-size: 16px;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar td {
  background: none !important;
  border: 0px none;
  padding: 10px;
  box-shadow: none !important;
  color: #5F5A5A;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 17px;
  text-align: center;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar td:hover {
  color: #5F5A5A !important;
  background: none !important;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar td.xdsoft_current {
  position: relative;
  color: white !important;
  font-weight: normal;
}
.xdsoft_datetimepicker .xdsoft_datepicker .xdsoft_calendar td.xdsoft_current::after {
  content: "";
  display: block;
  width: 38px;
  height: 38px;
  position: absolute;
  top: 0;
  left: -1px;
  z-index: -1;
  background-color: #008060;
  border-radius: 50%;
}
.xdsoft_datetimepicker .xdsoft_calendar td.xdsoft_today {
  color: #008060;
}
.xdsoft_datetimepicker .xdsoft_calendar td.xdsoft_default,
.xdsoft_datetimepicker .xdsoft_calendar td.xdsoft_current,
.xdsoft_datetimepicker .xdsoft_timepicker .xdsoft_time_box > div > div.xdsoft_current {
  background: #008060;
  color: white;
}

.modal.fade.show {
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 1 !important;
}
.modal.fade.show .modal-dialog {
  margin: 100px auto;
  transform: none;
}

.colorpicker-bs-popover {
  opacity: 1 !important;
}

.e404 {
  margin-left: -275px !important;
}

.daterangepicker .ranges li.active, .daterangepicker td.active {
  background-color: #008060 !important;
}
.daterangepicker .drp-buttons {
  display: flex !important;
  flex-direction: row-reverse !important;
  justify-content: space-between !important;
  align-items: center !important;
}
.daterangepicker .drp-buttons .drp-selected {
  margin-left: auto !important;
}

.cropperImage img {
  max-height: 400px !important;
}

.block-editor-block-list__layout ul {
  list-style: unset !important;
}

.form-group .input-group-append .input-group-text.colorpicker-input-addon {
  top: 0;
  left: auto;
  min-width: 0;
}

.sidebar-menu i[class^=icon-], .sidebar-menu i.fa {
  position: absolute;
  width: 20px;
  height: 20px;
}

i[class^=icon-] {
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

i.icon-control-panel {
  background-image: url("../../images/icon/icone-tableau-de-bord.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-contents {
  background-image: url("../../images/icon/icone-contenus.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-team {
  background-image: url("../../images/icon/icone-equipe.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-blog {
  background-image: url("../../images/icon/icone-blogue.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-inscriptions {
  background-image: url("../../images/icon/icone-inscriptions.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-neighborhoods {
  background-image: url("../../images/icon/icone-quartiers.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-projects {
  background-image: url("../../images/icon/icone-projets-immobiliers.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-homestaging {
  background-image: url("../../images/icon/icone-homestaging.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-distinctions {
  background-image: url("../../images/icon/icone-distinctions.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-testimonies {
  background-image: url("../../images/icon/icone-temoignages.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-partners {
  background-image: url("../../images/icon/icone-partenaires.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-featured {
  background-image: url("../../images/icon/icone-blocs-vedettes.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-contact {
  background-image: url("../../images/icon/icone-demande-contact.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-reports {
  background-image: url("../../images/icon/icone-rapports-visites.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-publications {
  background-image: url("../../images/icon/icone-publications.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-age {
  background-image: url("../../images/icon/icone-age.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-duration {
  background-image: url("../../images/icon/icone-duree.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-visits {
  background-image: url("../../images/icon/icone-visites.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
i.icon-user {
  background-image: url("../../images/icon/icone-user.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.dash-tabs-ctn {
  display: flex;
  flex-wrap: wrap;
}

.dash-tabs-ctn .nav-tabs, .performances-ctn .nav-tabs, .campaign-ctn .nav-tabs {
  width: 100%;
  background-color: #ECF0F6;
}
.dash-tabs-ctn .nav-item, .performances-ctn .nav-item, .campaign-ctn .nav-item {
  margin: 0;
  border: 0 none;
}
.dash-tabs-ctn .nav-item .nav-link, .performances-ctn .nav-item .nav-link, .campaign-ctn .nav-item .nav-link {
  height: 70px;
  margin: 0;
  padding: 18px 40px;
  border: 0 none;
  border-top: 6px solid #ECF0F6;
  border-radius: 0;
  font-family: "Open Sans";
  font-size: 17px;
  font-weight: 600;
  line-height: 23px;
  color: #3D3F43;
}
.dash-tabs-ctn .nav-item .nav-link.active, .dash-tabs-ctn .nav-item .nav-link:hover, .performances-ctn .nav-item .nav-link.active, .performances-ctn .nav-item .nav-link:hover, .campaign-ctn .nav-item .nav-link.active, .campaign-ctn .nav-item .nav-link:hover {
  border-top-color: #E4B448;
  color: #937227;
}
.dash-tabs-ctn .analytics-filter-ctn, .performances-ctn .analytics-filter-ctn, .campaign-ctn .analytics-filter-ctn {
  padding: 0px;
  margin-bottom: 50px;
}
.dash-tabs-ctn .analytics-filter-ctn .search-properties-ctn, .performances-ctn .analytics-filter-ctn .search-properties-ctn, .campaign-ctn .analytics-filter-ctn .search-properties-ctn {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin: 0px;
}
.dash-tabs-ctn .analytics-filter-ctn .search-properties-ctn .form-group, .performances-ctn .analytics-filter-ctn .search-properties-ctn .form-group, .campaign-ctn .analytics-filter-ctn .search-properties-ctn .form-group {
  width: 160px;
  margin: 0 !important;
}
.dash-tabs-ctn .analytics-filter-ctn .search-properties-ctn .btn-primary, .performances-ctn .analytics-filter-ctn .search-properties-ctn .btn-primary, .campaign-ctn .analytics-filter-ctn .search-properties-ctn .btn-primary {
  height: 45px;
}
.dash-tabs-ctn .tab-content, .performances-ctn .tab-content, .campaign-ctn .tab-content {
  padding: 0px !important;
  background: none;
  border: 0 none;
  width: 100%;
  margin-top: 30px;
  box-shadow: none;
}
.dash-tabs-ctn .content-bloc, .performances-ctn .content-bloc, .campaign-ctn .content-bloc {
  padding: 50px;
  background-color: #FFFFFF;
  box-shadow: 0 7px 15px 0 rgba(0, 0, 0, 0.05);
}
.dash-tabs-ctn .content-bloc .bloc-header, .performances-ctn .content-bloc .bloc-header, .campaign-ctn .content-bloc .bloc-header {
  flex: 1 0 100%;
  text-align: left;
  margin-bottom: 30px;
}
.dash-tabs-ctn .content-bloc .bloc-header .title, .performances-ctn .content-bloc .bloc-header .title, .campaign-ctn .content-bloc .bloc-header .title {
  margin: 0;
}
.dash-tabs-ctn .content-bloc .title, .performances-ctn .content-bloc .title, .campaign-ctn .content-bloc .title {
  margin: 0 0 30px;
  font-size: 22px;
  font-weight: bold;
  line-height: 30px;
}
.dash-tabs-ctn .content-bloc.plain, .performances-ctn .content-bloc.plain, .campaign-ctn .content-bloc.plain {
  box-shadow: none;
  padding: 12px;
}
.dash-tabs-ctn .content-bloc.plain .title, .performances-ctn .content-bloc.plain .title, .campaign-ctn .content-bloc.plain .title {
  font-size: 18px;
}
.dash-tabs-ctn .content-bloc.plain .title small, .performances-ctn .content-bloc.plain .title small, .campaign-ctn .content-bloc.plain .title small {
  display: block;
  color: #008060;
  font-weight: 700;
}
.dash-tabs-ctn .content-bloc.plain .title a, .performances-ctn .content-bloc.plain .title a, .campaign-ctn .content-bloc.plain .title a {
  color: #008060;
}
.dash-tabs-ctn .numbers-info, .performances-ctn .numbers-info, .campaign-ctn .numbers-info {
  display: flex;
  align-items: center;
  text-align: center;
  flex-wrap: wrap;
}
.dash-tabs-ctn .numbers-info.-two-columns .number-item, .performances-ctn .numbers-info.-two-columns .number-item, .campaign-ctn .numbers-info.-two-columns .number-item {
  width: 50%;
}
.dash-tabs-ctn .numbers-info .number-item, .performances-ctn .numbers-info .number-item, .campaign-ctn .numbers-info .number-item {
  width: 33.3333333333%;
  padding: 20px;
  border-right: solid 1px #D8D8D8;
}
.dash-tabs-ctn .numbers-info .number-item:last-child, .performances-ctn .numbers-info .number-item:last-child, .campaign-ctn .numbers-info .number-item:last-child {
  border-right: 0;
}
.dash-tabs-ctn .numbers-info .number-item .number, .performances-ctn .numbers-info .number-item .number, .campaign-ctn .numbers-info .number-item .number {
  margin: 10px 0px 20px;
  font-size: 26px;
  font-weight: 600;
  line-height: 36px;
}
.dash-tabs-ctn .numbers-info .number-item .legend, .performances-ctn .numbers-info .number-item .legend, .campaign-ctn .numbers-info .number-item .legend {
  margin: 0;
  font-size: 11px;
  line-height: 15px;
  letter-spacing: 1px;
  font-weight: 600;
  color: #8898AA;
  text-transform: uppercase;
}
.dash-tabs-ctn .numbers-info .number-item i, .performances-ctn .numbers-info .number-item i, .campaign-ctn .numbers-info .number-item i {
  width: 32px;
  height: 33px;
  margin: 0 auto 10px;
}
.dash-tabs-ctn .sell-graph, .performances-ctn .sell-graph, .campaign-ctn .sell-graph {
  margin-top: 50px;
}
.dash-tabs-ctn .sell-graph #soldedGraph, .performances-ctn .sell-graph #soldedGraph, .campaign-ctn .sell-graph #soldedGraph {
  margin-top: 60px;
}
.dash-tabs-ctn .graphs-ctn, .performances-ctn .graphs-ctn, .campaign-ctn .graphs-ctn {
  display: flex;
  justify-content: space-between;
  padding-top: 50px;
}
.dash-tabs-ctn .graphs-ctn .graph-bloc, .performances-ctn .graphs-ctn .graph-bloc, .campaign-ctn .graphs-ctn .graph-bloc {
  width: calc(50% - 20px);
}
.dash-tabs-ctn .graphs-ctn .graph-bloc .row, .performances-ctn .graphs-ctn .graph-bloc .row, .campaign-ctn .graphs-ctn .graph-bloc .row {
  display: flex;
  align-items: center;
}
.dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-chart, .performances-ctn .graphs-ctn .graph-bloc .doughnut-chart, .campaign-ctn .graphs-ctn .graph-bloc .doughnut-chart {
  max-width: 250px;
  margin: 0 0 0 auto;
}
.dash-tabs-ctn .list-stats-ctn, .performances-ctn .list-stats-ctn, .campaign-ctn .list-stats-ctn {
  margin: 0;
  padding: 0;
  list-style: none;
}
.dash-tabs-ctn .list-stats-ctn li, .performances-ctn .list-stats-ctn li, .campaign-ctn .list-stats-ctn li {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 30px;
  padding-bottom: 30px;
  border-bottom: solid 1px #D8D8D8;
}
.dash-tabs-ctn .list-stats-ctn li .label-ctn, .performances-ctn .list-stats-ctn li .label-ctn, .campaign-ctn .list-stats-ctn li .label-ctn {
  display: flex;
  align-items: center;
  width: calc(100% - 50px);
}
.dash-tabs-ctn .list-stats-ctn li .label-ctn .color-legend, .performances-ctn .list-stats-ctn li .label-ctn .color-legend, .campaign-ctn .list-stats-ctn li .label-ctn .color-legend {
  margin-right: 15px;
  display: block;
  width: 20px;
}
.dash-tabs-ctn .list-stats-ctn li .label, .performances-ctn .list-stats-ctn li .label, .campaign-ctn .list-stats-ctn li .label {
  height: 20px;
  font-size: 15px;
  color: #64696F;
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  padding: 0 10px 0 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.dash-tabs-ctn .list-stats-ctn li .data, .performances-ctn .list-stats-ctn li .data, .campaign-ctn .list-stats-ctn li .data {
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  text-align: right;
  font-family: "Open Sans", sans-serif;
}
.dash-tabs-ctn .list-stats-ctn li.small, .performances-ctn .list-stats-ctn li.small, .campaign-ctn .list-stats-ctn li.small {
  margin-top: 15px;
  padding-bottom: 15px;
}
.dash-tabs-ctn .list-info-full, .performances-ctn .list-info-full, .campaign-ctn .list-info-full {
  padding-top: 25px;
  padding-right: 5px;
  padding-left: 0;
}
.dash-tabs-ctn .list-info-full .list-stats-ctn, .performances-ctn .list-info-full .list-stats-ctn, .campaign-ctn .list-info-full .list-stats-ctn {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
}
.dash-tabs-ctn .list-info-full .list-stats-ctn li, .performances-ctn .list-info-full .list-stats-ctn li, .campaign-ctn .list-info-full .list-stats-ctn li {
  width: calc(33.3333333333% - 40px);
  margin-right: 60px;
}
.dash-tabs-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3), .performances-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3), .campaign-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3) {
  margin-right: 0;
}
.dash-tabs-ctn .visits-chart-ctn, .performances-ctn .visits-chart-ctn, .campaign-ctn .visits-chart-ctn {
  padding-top: 15px;
  padding-right: 5px;
  padding-left: 0;
}
.dash-tabs-ctn .visits-chart-ctn .line-chart, .performances-ctn .visits-chart-ctn .line-chart, .campaign-ctn .visits-chart-ctn .line-chart {
  margin-top: 25px;
}
@media (max-width: 1400px) {
  .dash-tabs-ctn .graphs-ctn .graph-bloc .row, .performances-ctn .graphs-ctn .graph-bloc .row, .campaign-ctn .graphs-ctn .graph-bloc .row {
    flex-direction: column-reverse;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .col-md-6, .performances-ctn .graphs-ctn .graph-bloc .col-md-6, .campaign-ctn .graphs-ctn .graph-bloc .col-md-6 {
    width: 100%;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-chart, .performances-ctn .graphs-ctn .graph-bloc .doughnut-chart, .campaign-ctn .graphs-ctn .graph-bloc .doughnut-chart {
    margin: 0 auto;
  }
}
@media (max-width: 1200px) {
  .dash-tabs-ctn .list-info-full .list-stats-ctn li, .performances-ctn .list-info-full .list-stats-ctn li, .campaign-ctn .list-info-full .list-stats-ctn li {
    width: calc(50% - 30px);
    margin-right: 60px;
  }
  .dash-tabs-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3), .performances-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3), .campaign-ctn .list-info-full .list-stats-ctn li:nth-child(3n+3) {
    margin-right: 60px;
  }
  .dash-tabs-ctn .list-info-full .list-stats-ctn li:nth-child(even), .performances-ctn .list-info-full .list-stats-ctn li:nth-child(even), .campaign-ctn .list-info-full .list-stats-ctn li:nth-child(even) {
    margin-right: 0;
  }
}
@media (max-width: 1080px) {
  .dash-tabs-ctn .graphs-ctn, .performances-ctn .graphs-ctn, .campaign-ctn .graphs-ctn {
    flex-wrap: wrap;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc, .performances-ctn .graphs-ctn .graph-bloc, .campaign-ctn .graphs-ctn .graph-bloc {
    width: 100%;
    margin-bottom: 60px;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc:last-child, .performances-ctn .graphs-ctn .graph-bloc:last-child, .campaign-ctn .graphs-ctn .graph-bloc:last-child {
    margin-bottom: 0;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .row, .performances-ctn .graphs-ctn .graph-bloc .row, .campaign-ctn .graphs-ctn .graph-bloc .row {
    flex-direction: row;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .col-md-6, .performances-ctn .graphs-ctn .graph-bloc .col-md-6, .campaign-ctn .graphs-ctn .graph-bloc .col-md-6 {
    width: 100%;
  }
}
@media only screen and (max-width: 767px) {
  .dash-tabs-ctn .nav-item .nav-link, .performances-ctn .nav-item .nav-link, .campaign-ctn .nav-item .nav-link {
    height: auto;
    padding: 15px 16px;
    font-size: 14px;
  }
  .dash-tabs-ctn .tab-content .content-bloc, .performances-ctn .tab-content .content-bloc, .campaign-ctn .tab-content .content-bloc {
    padding: 30px 20px;
  }
  .dash-tabs-ctn .tab-content .numbers-info, .performances-ctn .tab-content .numbers-info, .campaign-ctn .tab-content .numbers-info {
    flex-wrap: wrap;
    justify-content: center;
  }
  .dash-tabs-ctn .tab-content .numbers-info .number-item, .performances-ctn .tab-content .numbers-info .number-item, .campaign-ctn .tab-content .numbers-info .number-item {
    width: calc(50% - 20px);
    margin-bottom: 30px;
  }
  .dash-tabs-ctn .tab-content .numbers-info .number-item:nth-child(even), .performances-ctn .tab-content .numbers-info .number-item:nth-child(even), .campaign-ctn .tab-content .numbers-info .number-item:nth-child(even) {
    border-right: 0;
  }
  .dash-tabs-ctn .tab-content .numbers-info .number-item:last-child, .performances-ctn .tab-content .numbers-info .number-item:last-child, .campaign-ctn .tab-content .numbers-info .number-item:last-child {
    margin-bottom: 0;
    margin-top: 20px;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .row, .performances-ctn .graphs-ctn .graph-bloc .row, .campaign-ctn .graphs-ctn .graph-bloc .row {
    flex-direction: column-reverse;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .col-md-6, .performances-ctn .graphs-ctn .graph-bloc .col-md-6, .campaign-ctn .graphs-ctn .graph-bloc .col-md-6 {
    width: 100%;
  }
  .dash-tabs-ctn .list-info-full .list-stats-ctn li, .performances-ctn .list-info-full .list-stats-ctn li, .campaign-ctn .list-info-full .list-stats-ctn li {
    width: 100%;
    margin-right: 0 !important;
  }
}
@media only screen and (max-width: 550px) {
  .dash-tabs-ctn .analytics-filter-ctn .search-properties-ctn, .performances-ctn .analytics-filter-ctn .search-properties-ctn, .campaign-ctn .analytics-filter-ctn .search-properties-ctn {
    justify-content: center;
    padding: 40px 14px 0;
  }
}
@media only screen and (max-width: 400px) {
  .dash-tabs-ctn .nav-item .nav-link, .performances-ctn .nav-item .nav-link, .campaign-ctn .nav-item .nav-link {
    font-size: 11px;
  }
}

#campaigns .add-button {
  display: flex;
  flex-wrap: wrap;
  min-width: 100%;
  margin-bottom: 20px;
  justify-content: flex-end;
}
#campaigns .content-bloc {
  overflow: hidden;
}
#campaigns .content-bloc.campaign-elem {
  margin-bottom: 20px;
  position: relative;
  padding: 0;
}
#campaigns .content-bloc .card-header, #campaigns .content-bloc .card-body {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  padding: 15px 27px;
}
#campaigns .content-bloc .card-header .datetime, #campaigns .content-bloc .card-body .datetime {
  margin: 0;
}
#campaigns .content-bloc .card-header .badge-pill, #campaigns .content-bloc .card-body .badge-pill {
  border-radius: 10rem;
  font-weight: 700;
  font-size: 13px;
  line-height: 16px;
  padding: 5px 10px;
}
#campaigns .content-bloc .card-header .badge-pill i, #campaigns .content-bloc .card-body .badge-pill i {
  margin-right: 6px;
}
#campaigns .content-bloc .card-actions {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  padding: 22px 27px;
}
#campaigns .content-bloc .card-actions a {
  color: #000000;
}
#campaigns .content-bloc .card-actions a .la-angle-up {
  font-size: 16px;
  margin-left: 10px;
  transition: all 0.4s ease;
}
#campaigns .content-bloc .card-actions a.collapsed .la-angle-up {
  transform: rotate(180deg);
}
#campaigns .content-bloc .card-actions .button-action {
  display: flex;
  flex-wrap: nowrap;
}
#campaigns .content-bloc .card-actions .button-action .btn {
  border: 1px solid hsla(210, 5%, 55%, 0.3);
  border-radius: 5px;
  font-weight: 600;
  color: #858B91;
  font-size: 13px;
  line-height: 18px;
  margin-left: 10px;
}
#campaigns .content-bloc .card-actions .button-action .btn:hover {
  border-color: #008060;
  background-color: #008060;
  color: white;
}
#campaigns .content-bloc .card-actions .button-action .btn:hover i {
  color: white;
}
#campaigns .content-bloc .card-actions .button-action .btn i {
  width: 100%;
  color: #858B91;
  font-size: 20px;
}
#campaigns .content-bloc .card-actions .button-action .btn i.la-edit {
  margin-right: 0;
}
#campaigns .content-bloc .delete-action {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30px;
}
#campaigns .content-bloc .delete-action .btn-delete {
  border: solid 1px rgba(0, 0, 0, 0.6);
  color: rgba(0, 0, 0, 0.6);
  box-shadow: none;
  font-weight: 700;
}
#campaigns .content-bloc .delete-action .btn-delete i {
  font-size: 20px;
}
#campaigns .content-bloc .bg-color {
  background-color: rgba(238, 240, 242, 0.5);
  padding: 30px;
}
#campaigns .content-bloc .bg-color .title span {
  font-weight: 600;
  font-size: 11px;
  line-height: 15px;
  color: #8898AA;
}

html.interface-interface-skeleton__html-container {
  position: initial !important;
}

.dashboard-analytics {
  margin: 0;
  padding: 0;
  background-color: #ecf0f5;
}
.dashboard-analytics div {
  box-sizing: border-box;
}
.dashboard-analytics .dashboard {
  max-width: 80%;
  margin: 80px auto;
}
.dashboard-analytics .dashboard .header {
  display: flex;
  justify-content: space-between;
}
.dashboard-analytics .dashboard .header .header-title .title {
  margin: 0;
  font-size: 42px;
  font-weight: 600;
}
.dashboard-analytics .dashboard .header .header-title .date {
  font-size: 22px;
  margin: 10px 0 0;
  color: #64696F;
}
.dashboard-analytics .dashboard .header .logo-ctn img {
  max-width: 300px;
}
.dashboard-analytics .dashboard .header.fake-header {
  margin-top: 40px;
  display: none;
}
.dashboard-analytics .dashboard .header.fake-header .header-title .title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}
.dashboard-analytics .dashboard .header.fake-header .header-title .date {
  font-size: 20px;
  margin: 10px 0 0;
  color: #64696F;
}
.dashboard-analytics .dashboard .header.fake-header .logo-ctn img {
  max-width: 200px;
}
.dashboard-analytics .dashboard .header.fake-header .property-info {
  margin-top: 10px;
}
.dashboard-analytics .dashboard .header.fake-header .property-info .address {
  margin: 0;
  color: black;
  font-weight: 600;
  font-size: 16px;
}
.dashboard-analytics .dashboard .header.fake-header .property-info .mls {
  color: #64696F;
  text-transform: uppercase;
  font-size: 14px;
  margin-top: 5px;
}
.dashboard-analytics .dashboard .property-ctn {
  margin: 70px 0 30px;
  display: flex;
}
.dashboard-analytics .dashboard .property-ctn .property-img img {
  max-width: 230px;
}
.dashboard-analytics .dashboard .property-ctn .property-info {
  margin-left: 40px;
}
.dashboard-analytics .dashboard .property-ctn .property-info .address {
  margin: 0;
  color: black;
  font-weight: 600;
  font-size: 27px;
}
.dashboard-analytics .dashboard .property-ctn .property-info .mls {
  color: #64696F;
  text-transform: uppercase;
  font-size: 20px;
}
.dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc:first-child {
  margin-right: 40px;
}
.dashboard-analytics .dashboard .filter-gray {
  filter: brightness(0) saturate(100%) invert(50%) sepia(1%) saturate(5550%) hue-rotate(163deg) brightness(86%) contrast(90%);
}
@media (min-width: 1400px) {
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc .row .col-md-6 {
    width: 50%;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-ctn {
    padding-left: 40px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-ctn .doughnut-chart {
    max-width: 250px;
  }
}
@media (max-width: 1200px) {
  .dashboard-analytics .dashboard {
    max-width: 100%;
    margin: 60px 30px;
  }
  .dashboard-analytics .dashboard .header .header-title .title {
    font-size: 38px;
  }
  .dashboard-analytics .dashboard .header .header-title .date {
    font-size: 20px;
  }
  .dashboard-analytics .dashboard .header .logo-ctn img {
    max-width: 250px;
  }
}
@media (max-width: 1080px) {
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc:first-child {
    margin-right: 0;
  }
}
@media only screen and (max-width: 768px) {
  .dashboard-analytics .dashboard .property-ctn .property-info .address {
    font-size: 24px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc .row .col-md-6 {
    width: 100%;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content {
    padding-top: 0;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content .numbers-info {
    padding: 30px 20px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content .numbers-info .number-item {
    display: flex;
    width: 100%;
    align-items: center;
    border-right: 0;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: solid 1px #D8D8D8;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content .numbers-info .number-item .icon {
    margin: 0;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content .numbers-info .number-item .number {
    margin: 0 25px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .tab-content .numbers-info .number-item:last-child {
    padding-bottom: 0;
    margin: 0;
    border: 0;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .content-bloc,
  .dashboard-analytics .dashboard .dash-tabs-ctn .list-info-full .content-bloc,
  .dashboard-analytics .dashboard .dash-tabs-ctn .visits-chart-ctn .content-bloc {
    padding: 30px 20px;
    margin-bottom: 50px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .content-bloc .title,
  .dashboard-analytics .dashboard .dash-tabs-ctn .list-info-full .content-bloc .title,
  .dashboard-analytics .dashboard .dash-tabs-ctn .visits-chart-ctn .content-bloc .title {
    text-align: center;
    font-size: 20px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-chart,
  .dashboard-analytics .dashboard .dash-tabs-ctn .list-info-full .graph-bloc .doughnut-chart,
  .dashboard-analytics .dashboard .dash-tabs-ctn .visits-chart-ctn .graph-bloc .doughnut-chart {
    max-width: 170px;
    max-height: 170px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .graphs-ctn .list-stats-ctn li,
  .dashboard-analytics .dashboard .dash-tabs-ctn .list-info-full .list-stats-ctn li,
  .dashboard-analytics .dashboard .dash-tabs-ctn .visits-chart-ctn .list-stats-ctn li {
    margin-top: 20px;
    padding-bottom: 15px;
  }
  .dashboard-analytics .dashboard .dash-tabs-ctn .visits-chart-ctn,
  .dashboard-analytics .dashboard .dash-tabs-ctn .list-info-full {
    padding-top: 0;
  }
}
@media only screen and (max-width: 500px) {
  .dashboard-analytics .dashboard {
    margin: 30px;
  }
  .dashboard-analytics .dashboard .header {
    flex-direction: column-reverse;
    margin-top: 30px;
  }
  .dashboard-analytics .dashboard .header .header-title .title {
    font-size: 22px;
  }
  .dashboard-analytics .dashboard .header .header-title .date {
    font-size: 18px;
  }
  .dashboard-analytics .dashboard .header .logo-ctn {
    text-align: center;
    margin-bottom: 40px;
  }
  .dashboard-analytics .dashboard .header .logo-ctn img {
    max-width: 200px;
  }
  .dashboard-analytics .dashboard .property-ctn {
    flex-wrap: wrap;
    margin-top: 30px;
  }
  .dashboard-analytics .dashboard .property-ctn .property-info {
    margin: 20px 0 0;
  }
  .dashboard-analytics .dashboard .property-ctn .property-info .address {
    font-size: 20px;
  }
  .dashboard-analytics .dashboard .property-ctn .property-info .mls {
    font-size: 16px;
  }
  .dashboard-analytics .dashboard .property-ctn .property-img {
    width: 100%;
  }
  .dashboard-analytics .dashboard .property-ctn .property-img img {
    max-width: 100%;
    width: 100%;
  }
}

@media print {
  @page {
    size: portrait;
    margin: 0.5cm;
    font-size: 12pt;
  }
  body {
    line-height: 1.3;
    background: white !important;
    color: #000;
  }
  .no-print {
    display: none !important;
  }
  .label {
    border: 0 !important;
  }
  a[href]:after {
    content: none !important;
  }
  .main-footer {
    display: none;
  }
  .reports-preview .table tbody > tr {
    display: none;
  }
  .reports-preview .table tbody > tr:nth-child(11) {
    display: table-row;
  }
  .reports-preview .table tbody > tr:nth-child(11) > td:nth-child(1) {
    display: none;
  }
  .reports-preview .table tbody > tr table tr {
    display: table-row !important;
  }
  .monthly-reports-preview .table tbody > tr {
    display: none;
  }
  .monthly-reports-preview .table tbody > tr:nth-child(4) {
    display: table-row;
  }
  .monthly-reports-preview .table tbody > tr:nth-child(4) > td:nth-child(1) {
    display: none;
  }
  .monthly-reports-preview .table tbody > tr table tr {
    display: table-row;
  }
  .monthly-reports-preview .table tbody > tr table tr:nth-child(4) > td:nth-child(1) {
    display: table-cell;
  }
  .content {
    padding: 0;
    width: 100%;
    margin: 0;
    float: none;
  }
  .dashboard-analytics .dashboard {
    max-width: 100%;
  }
  .dashboard-analytics .dashboard .header.fake-header {
    display: flex;
  }
  .dash-tabs-ctn .input-tab,
  .dash-tabs-ctn .tab-label,
  .dash-tabs-ctn .form {
    display: none !important;
  }
  .dash-tabs-ctn .tab-content {
    padding-top: 0;
    background-color: white;
  }
  .dash-tabs-ctn .tab-content .content-bloc {
    border: solid 1px #D8D8D8;
    box-shadow: none;
    padding: 20px;
    margin: 10px 0;
  }
  .dash-tabs-ctn .tab-content .content-bloc .title {
    font-size: 18px;
    margin-bottom: 10px;
  }
  .dash-tabs-ctn .tab-content .content-bloc .list-stats-ctn li {
    padding-bottom: 10px;
    margin-top: 10px;
  }
  .dash-tabs-ctn .tab-content .content-bloc .list-stats-ctn li .label-ctn .color-legend {
    width: 15px;
    margin-right: 10px;
  }
  .dash-tabs-ctn .tab-content .content-bloc .list-stats-ctn li .label {
    font-size: 12px;
    line-height: 16px;
  }
  .dash-tabs-ctn .tab-content .content-bloc .list-stats-ctn li .data {
    font-size: 14px;
  }
  .dash-tabs-ctn .tab-content .numbers-info {
    margin: 10px 0;
  }
  .dash-tabs-ctn .tab-content .numbers-info .number-item .number {
    font-size: 20px;
    line-height: 20px;
    margin: 10px 0;
  }
  .dash-tabs-ctn .graphs-ctn {
    padding-top: 15px;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc {
    width: 45%;
    float: left;
    margin: 10px 0;
    padding: 20px;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .row {
    flex-direction: column-reverse !important;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .title {
    text-align: center;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .col-md-6 {
    max-width: none !important;
    width: 100% !important;
    flex-basis: 100%;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .row .doughnut-ctn {
    text-align: right;
    max-width: 150px;
    margin: 30px 0;
    max-width: 150px !important;
    padding: 0 !important;
  }
  .dash-tabs-ctn .graphs-ctn .graph-bloc .doughnut-chart {
    width: 150px !important;
    height: auto !important;
  }
  .dash-tabs-ctn .list-info-full {
    padding-top: 15px;
    page-break-after: always;
  }
  .dash-tabs-ctn .visits-chart-ctn {
    padding-top: 15px;
  }
  .dash-tabs-ctn .visits-chart-ctn .content-bloc {
    border: 0;
  }
  .dash-tabs-ctn .visits-chart-ctn .content-bloc canvas {
    padding: 0 110px 80px 50px;
  }
  .dash-tabs-ctn .visits-chart-ctn .content-bloc .title {
    text-align: center;
  }
}
