/******/ (() => { // webpackBootstrap
/*!****************************************!*\
  !*** ./resources/assets/js/sidebar.js ***!
  \****************************************/
// This scripts overrides sidebar visibility settings to prevent a bug when window is resized
$(document).ready(function () {
  var mobileWidth = 992;
  function manageSidebar() {
    var mobile = $(this).width() < mobileWidth;
    sessionStorage.setItem('sidebar-collapsed', mobile ? 0 : 1);
  }
  $(window).on('load', manageSidebar);
  $(window).on('resize', manageSidebar);
});
/******/ })()
;