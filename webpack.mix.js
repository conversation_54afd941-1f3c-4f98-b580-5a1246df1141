const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js('resources/assets/js/app.js', 'public/js')
   .js('resources/assets/js/sidebar.js', 'public/js')

   // Copy reports scripts without webpack (prevent select2 bug)
   .copy('resources/assets/js/select2-4.0.12.min.js', 'public/js')
   .copy('resources/assets/js/part/info-help.js', 'public/js/custom/part/info-help.js')
   .copy('resources/assets/js/part/chart-construct.js', 'public/js/custom/part/chart-construct.js')
   .copy('resources/assets/js/admin/landingpage.js', 'public/js/admin/landingpage.js')
   .copy('resources/assets/js/reports.js', 'public/js')

   // Compile SASS files
   .sass('resources/assets/sass/app.scss', 'public/css')
   .sass('resources/assets/sass/laraberg-custom.scss', 'public/css')
   .sass('resources/assets/sass/pdf.scss', 'public/css')
   .sass('resources/assets/sass/analytics.scss', 'public/css')

   // Options for production builds
   .options({
     processCssUrls: false
   });

// Enable versioning in production
if (mix.inProduction()) {
   mix.version();
}
