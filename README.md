# E-closion v3

## Requirements
- PHP > 7.4
- composer 2
- mysql

## Installation
For starting, project, it's more easy to using current prod database. Migration are uge and not realy stable.
```bash
# Add the required folder structure
mkdir bootstrap/cache && mkdir storage/framework && cd storage/framework && mkdir cache && mkdir sessions && mkdir views

# set up your .env
cp .env.example .env

# modify WRIKE_API_KEY in your .env, put the wrike api key that will be used
composer install
# generate app key (laravel specific)
php artisan key:generate

# Migrate all the database
php artisan migrate
```
You will also need to import centris data. Custom artisan commands have been created for this purpose.

```bash
# Copy init files in the directory used byt the command
cp storage/import/init/* storage/import
# Run import
php artisan eclosion:import-data-centris {--step=execute this step} {--from=start from this step} {--mode=staging}
# then you can import last centris file with the same command, putting th files in storage/import(optional)
#seed
php artisan db:seed
```

If you want know all informations sent by <PERSON><PERSON><PERSON> in files, they're accessible [Centris files XLSX and Microsoft Access](https://drive.google.com/drive/folders/1IkX8zMDWXbX_HOW8pP0fLISo0ib0R32n?usp=sharing) on format CSV and Microsoft Access

---
## Compilation des styles front-end

### Prérequis
- Node.js (version 16+ recommandée)
- npm ou yarn

### Installation des dépendances
```bash
npm install
```

### Commandes de compilation

#### Développement
Pour compiler les assets en mode développement :
```bash
npm run dev
```

#### Mode watch (recommandé pour le développement)
Pour compiler automatiquement lors des modifications :
```bash
npm run watch
```

#### Mode hot reload
Pour le rechargement automatique avec hot module replacement :
```bash
npm run hot
```

#### Production
Pour minifier et optimiser les assets pour la production :
```bash
npm run prod
```

### Développement en parallèle
Pour développer efficacement, lancez en parallèle :
```bash
php artisan serve
npm run watch
```

### Structure des assets

#### Fichiers sources
- **JavaScript** : `resources/assets/js/`
  - `app.js` - Script principal de l'application
  - `sidebar.js` - Scripts pour la sidebar
  - `part/` - Scripts modulaires
  - `admin/` - Scripts spécifiques à l'administration

- **SCSS** : `resources/assets/sass/`
  - `app.scss` - Styles principaux de l'application
  - `analytics.scss` - Styles pour les analytics
  - `pdf.scss` - Styles pour les PDF
  - `laraberg-custom.scss` - Styles pour l'éditeur Gutenberg
  - `configs/` - Variables et configurations
  - `components/` - Styles des composants

#### Fichiers compilés
Les assets compilés sont générés dans `public/` :
- `public/js/` - JavaScript compilé
- `public/css/` - CSS compilé

### Configuration Laravel Mix

Le projet utilise **Laravel Mix 6** avec **Bootstrap 5**. La configuration se trouve dans `webpack.mix.js`.

#### Fonctionnalités activées :
- Compilation SCSS vers CSS
- Bundling JavaScript avec Webpack
- Autoprefixer pour la compatibilité navigateurs
- Versioning automatique en production
- Hot reload en développement

### Personnalisation des styles Bootstrap

Les variables Bootstrap peuvent être personnalisées dans :
- `resources/assets/sass/configs/_bootstrap-variables.scss`

Les variables personnalisées du projet sont dans :
- `resources/assets/sass/configs/_variables.scss`

### Dépannage

#### Erreurs de compilation
Si vous rencontrez des erreurs de compilation :

1. **Vérifiez Node.js** :
```bash
node --version  # Doit être 16+
npm --version
```

2. **Réinstallez les dépendances** :
```bash
rm -rf node_modules package-lock.json
npm install
```

3. **Nettoyez le cache** :
```bash
npm run dev -- --progress
```

#### Problèmes de mémoire
Pour les gros projets, augmentez la mémoire Node.js :
```bash
export NODE_OPTIONS="--max-old-space-size=4096"
npm run prod
```

### Ajustements de style dans l'interface d'administration

#### Modification des styles CRUD
Les styles des tables et formulaires d'administration se trouvent dans :
- `resources/assets/sass/components/_crud.scss`

#### Personnalisation des couleurs
Les couleurs principales sont définies dans :
- `resources/assets/sass/configs/_variables.scss` (variables personnalisées)
- `resources/assets/sass/configs/_bootstrap-variables.scss` (overrides Bootstrap)

#### Styles spécifiques aux composants
- `_header.scss` - En-tête de l'administration
- `_sidebar.scss` - Menu latéral
- `_dashboard.scss` - Tableau de bord
- `_reports.scss` - Pages de rapports

#### Workflow de modification des styles

1. **Modifier les fichiers SCSS** dans `resources/assets/sass/`
2. **Compiler les assets** :
```bash
npm run watch  # Pour le développement
# ou
npm run prod   # Pour la production
```
3. **Vérifier les changements** dans l'interface d'administration
4. **Commiter les modifications** des fichiers sources ET compilés

#### Variables CSS personnalisées
Le projet utilise des variables CSS pour les couleurs principales :
```scss
:root {
    --bs-btn-color: #008060;
    --bs-btn-border-color: #008060;
    --bs-btn-hover-bg: #008060;
    // ... autres variables
}
```

#### Migration Bootstrap 5
Le projet a été migré vers Bootstrap 5. Les principales différences :
- `help-block` → `form-text`
- `custom-select` → `form-select`
- CDN DataTables mis à jour pour Bootstrap 5

#### Déploiement des styles
Avant de déployer en production :
1. Compilez les assets optimisés : `npm run prod`
2. Commitez les fichiers dans `public/css/` et `public/js/`
3. Vérifiez que le versioning fonctionne correctement

## Commit convention

Commits messages must have the following format: `[x][y](z) message` where :

x is the general category the commit is about, 

y is a more precise information 

and z is the type of work done. 

- x _must_ be one of the followings : commands, admin, api, doc, general
- y _must_ be the file name, the name of the model impacted by the changes, the api call url, or the database table name
- y _may_ be omitted if x if enough to describe the work done. 
- z _must_ be one of the following : fix, hotfix, add, edit, remove, code-style
- message _must_ be descriptive and give reasons for the change
- If several files/models are impacted, message _may_ follow the following format :   `[x1/x2][y1/y2](z) message`. / _must_ be used. \ _must not_ be used.

- Changelog entries _must_ follow the folling format : `[x\y] message`, \ _must_ be used, / _must not_ be used.
- Changelog entries _must_ Precise if a migration is necessary by the following manner : `[x\y](MIGRATION) message`.
- Changelog entries _must_ be about a unique combination of x and y. They _must not_ try to add several x or y as a commit _may_.
- Changleog entries _must_ comply to [Keep a Changelog](http://keepachangelog.com/en/1.0.0/).

## Documentation

The documentation about the API routes it's published here with postman: [PostMan documentation](https://documenter.getpostman.com/view/3816130/2s93RTPrk2)
All routes are here with there parameters and option.

## Other commands
`php artisan eclosion:import-brokers-to-sites` : Imports brokers to sites using office code.

## Import old inscriptions sold
`php artisan eclosion:import-old-inscriptions-sold --fk_site_id= --file_name=` : Import old inscriptions sold listed in CSV file for website.

copy csv file into `/storage/import/old_inscriptions_sold` folder before start script. The file must contain a header with 3 columns: mls, lat, lng.

## Billing inscriptions [Deprecated]

For creating billing per day by site in administration inscriptions panel, juste run this command one time per day:
```bash
php artisan eclosion:inscriptionbills
```
Billing price per inscription are define in `/config/eclosion.php`:
```
...
'billing_price' => 0.035
...
```
This script create a log for all inscriptions by site by day in other table. It update value by day.
If you want, you put clearing logs table with this command:
```bash
php artisan eclosion:clearinscriptionsbills
```
It delete all logs before today in database `inscriptions_bills_logs`.

## Billing inscriptions (SiteMonthlyBill)

-[TODO]

## Monthly report

The monthly report are divided in two command : 
- `/app/Console/Command/Reports/Create.php`
- `/app/Console/Command/Reports/Send.php`

The first file create a report in database with HTML mail template, one by contact mail and language. executed by this command

```bash
php artisan eclosion:create-monthly-reports
```

The second file is just a loop in the db `monthly_reports` and send all mail doesn't sent again. Note that that command needs CHROME_PATH to be set in env.
CHROME_PATH being the path to an executable google chrome - the command uses chrome in headless mode in order to capture a pdf of a report (with js charts).

```bash
php artisan eclosion:send-monthly-reports
```

## Seeds

To create 5 inscriptions openhouse a day for one month (for one MLS) : `db:seed --class="InscriptionsOpenHousesSeeder"` , MLS will be asked.

## Google Analytics

GA dashboard is working with [Spatie Laravel Analytics](https://github.com/spatie/laravel-analytics).
All documentation is provided here. 

Nevertheless, the dashboard needs the site to have analytics_view_id in database. This can be done under the admin
dashboard, Sites > [site to modify] "Modifier".

Also, we need the user `<EMAIL>` to be added to the view. Here are the instruction provided by Spatie:

Go to  [Analytics site](https://analytics.google.com/analytics). Go to "User management" in the Admin-section of the property.

![5](https://spatie.github.io/laravel-analytics/v2/5.jpg)

On this screen you can grant access to `<EMAIL>`. Read only access is enough.

![6](https://spatie.github.io/laravel-analytics/v2/6.jpg)

## Google Reviews 
The E-closion platform allows users to synchronize with our Google reviews:
To enable a website to display Google reviews, the site needs to have the location_id of the Google My Business account. This can be set under the admin dashboard: Sites > [site to modify] > "Modify". (Location ID is available from the GMB account)

Additionally, our Kryzalid Google My Business account (<EMAIL>) must be granted permission to edit the client's Google My Business account (Manager role is necessary).

If this is the first time you are initializing Google Reviews, you need to have the default credentials.json in storage/mybusiness/credentials.json (check in the [Google API console](https://console.cloud.google.com/apis/credentials?project=eclosionca-*************) or obtain it from the current production environment).


- To generate Google reviews for all sites, you can go to the custom route `/fr/admin/gmb`. If there is no token, it will prompt you to synchronize with a Google account (<NAME_EMAIL>). Otherwise, it will generate a JSON file of all reviews.

- If the token is no longer valid, you can generate a new one at the custom route `/fr/admin/gmb/refresh`.

- In production, a cron job is set to fetch Google reviews every Saturday (check the crontab for details).


## Analytics Pdf Generator

In inscriptions admin panel, one button create a Google analytics statistics report on PDF format. 

This features use [Wkhtmltopdf](https://wkhtmltopdf.org/) with [PHP WkHtmlToPdf](https://github.com/mikehaertl/phpwkhtmltopdf) package.

For installation, [Downmload](https://wkhtmltopdf.org/downloads.html) software and install package with composer if it's not already done.

This features create view in Larvael, one by page, and generate with this soft a good PDF with all paramters.

In `.env` file, variable `WKHTMLTOPDF_PATH` contein path for executable software. in server, it's in folder `/bin` in toot path project. For your confort, install WkHtmlToPdf in global on your computer and change this variable for the command.

For preview the html for pdf result, add `?debug=1` after your url (1 are fisrt page and 2 the second pages, yes, i know, i'm a genious)

@Todo: Generalised function for using in extern api
