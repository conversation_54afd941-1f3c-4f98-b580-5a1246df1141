<?php

namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Backpack\PermissionManager\app\Models\Permission;
use Backpack\PermissionManager\app\Models\Role;

class PermissionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('permissions')->delete();
        Permission::create(['name' => 'editableblock']);
        Permission::create(['name' => 'teammembers']);
        Permission::create(['name' => 'post']);
        Permission::create(['name' => 'inscription']);
        Permission::create(['name' => 'inscriptions_collaboration']);
        Permission::create(['name' => 'sitesneighborhood']);
        Permission::create(['name' => 'groupinscriptions']);
        Permission::create(['name' => 'homestaging']);
        Permission::create(['name' => 'award']);
        Permission::create(['name' => 'testimonials']);
        Permission::create(['name' => 'partners']);
        Permission::create(['name' => 'programs']);
        Permission::create(['name' => 'contact']);
        Permission::create(['name' => 'visit-reports']);
        Permission::create(['name' => 'publication']);
        Permission::create(['name' => 'sitesneighborhoodv2']);

        $adminRole = Role::findByName('admin');
        $adminRole->givePermissionTo(['editableblock', 'teammembers', 'post', 'inscription'
        , 'inscriptions_collaboration', 'sitesneighborhood', 'groupinscriptions', 'homestaging', 'award'
        , 'testimonials', 'partners', 'programs', 'contact', 'visit-reports', 'publication']);

        $clientRole = Role::findByName('client');
        $clientRole->givePermissionTo(['editableblock', 'teammembers', 'post', 'inscription'
        , 'homestaging', 'award', 'testimonials', 'partners', 'contact']);
    }
}
