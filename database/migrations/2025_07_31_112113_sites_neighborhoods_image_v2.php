<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SitesNeighborhoodsImageV2 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sites_neighborhoods_images_v2', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->string('path');
            $table->boolean('published')->default(true);
            $table->unsignedInteger('fk_site_neighborhood_id');
            $table->string('title')->nullable();
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();
            $table->tinyInteger('parent_id')->nullable();
            $table->timestamps();
        });


        // Foreign keys
        Schema::table('sites_neighborhoods_images_v2', function (Blueprint $table) {
            $table->foreign('fk_site_neighborhood_id')->references('id')->on('sites_neighborhoods_v2')->onDelete('cascade')->onUpdate('restrict');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sites_neighborhoods_images_v2');

    }
}
