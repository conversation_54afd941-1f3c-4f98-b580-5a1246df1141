<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterOrderingValues extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add default value to ordering column
        Schema::table('sites_neighborhoods_v2', function (Blueprint $table) {
            $table->integer('lft')->default(0)->change();
            $table->integer('rgt')->default(0)->change();
            $table->integer('depth')->default(0)->change();
        });
    }

    

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sites_neighborhoods_v2', function (Blueprint $table) {
            $table->integer('lft')->default(null)->change();
            $table->integer('rgt')->default(null)->change();
            $table->integer('depth')->default(null)->change();
        });
    }
}
