<?php
// Create with: php artisan make:migration create_sites_neighborhoods_v2_table

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSitesNeighborhoodsV2Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sites_neighborhoods_v2', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_site_id');
            $table->unsignedInteger('fk_eclosion_neighborhood_id');
            $table->unsignedInteger('fk_sites_neighborhood_id')->nullable(); // Link to the other language version
            $table->string('language', 2); // 'fr' or 'en'
            
            // Basic fields
            $table->string('name', 100);
            $table->string('slug', 100);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            
            // Content fields
            $table->longText('description')->nullable();
            $table->longText('content')->nullable(); // Laraberg content
            
            // Media fields
            $table->text('video_url')->nullable();
            $table->text('header_image')->nullable();
            $table->longText('points_of_interest')->nullable();
            
            // Geometry fields (copied from parent if exists)
            $table->string('geometry_type')->nullable();
            $table->longText('coordinates')->nullable();
            
            // Publishing
            $table->boolean('published')->default(true);
            $table->boolean('no_index')->default(false);

            // Reorder
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();
            $table->tinyInteger('parent_id')->nullable();

            //editable blocks
            $table->text('editableblocks')->nullable();
            
            $table->timestamps();
            
            // blog_posts_v2_fk_blog_post_id_foreign

            
            // Indexes
            $table->index(['fk_site_id', 'language']);
            $table->index(['slug', 'language']);
            $table->unique(['fk_site_id', 'fk_eclosion_neighborhood_id', 'language'], 'unique_site_neighborhood_language');
        });

         Schema::table('sites_neighborhoods_v2', function (Blueprint $table) {
                        // Foreign keys
            $table->foreign('fk_site_id')->references('id')->on('sites')->onDelete('cascade')->onUpdate('restrict');
            $table->foreign('fk_eclosion_neighborhood_id')->references('id')->on('eclosion_neighborhoods')->onDelete('restrict')->onUpdate('restrict');
            $table->foreign('fk_sites_neighborhood_id')->references('id')->on('sites_neighborhoods_v2')->onDelete('set null')->onUpdate('restrict');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sites_neighborhoods_v2', function (Blueprint $table) {
            $table->dropForeign(['fk_site_id']);
            $table->dropForeign(['fk_eclosion_neighborhood_id']);
            $table->dropForeign(['fk_sites_neighborhood_id']);
        });
        Schema::dropIfExists('sites_neighborhoods_v2');
    }
}
