<?php

// --------------------------
// Custom Backpack Routes
// --------------------------
// This route file is loaded automatically by Backpack\Base.
// Routes you generate using Backpack\Generators will be placed here.

use Illuminate\Support\Facades\Redirect;

Route::group([
    'prefix'     => '{locale}/' . config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array) config('backpack.base.web_middleware', 'web'),
        (array) config('backpack.base.middleware_key', 'admin'),
    ),
    'namespace'  => 'App\Http\Controllers\Admin',
], function () { // custom admin routes
    //----------
    // SEARCH AJAX TABLE
    //----------
    Route::get('ajax/inscriptions', [App\Http\Controllers\Api\InscriptionApiController::class, 'getByMls'])->name('ajax-inscriptions');
    Route::get('generate-pdf/{mls}/{start?}', [App\Http\Controllers\PdfController::class, 'generate']);
    Route::get('facebook-post/{mls}', [App\Http\Controllers\FacebookPostController::class, 'publish']);
    Route::get('monthlyreports/{reportid}/sendsample', [App\Http\Controllers\Api\MonthlyReportController::class, 'sendSample']);
    Route::get('ajax/inscription-groups', [App\Http\Controllers\Api\InscriptionGroupApiController::class, 'getByTitle']);

    //----------
    // CENTRIS
    //----------
    // Firms from centris
    Route::crud('firm', 'FirmCrudController');

    // Fixed Values from centris
    Route::crud('fixedvalue', 'FixedvalueCrudController');

    // Municipalities from centris
    Route::crud('municipality', 'MunicipalityCrudController');

    // Regions from centris
    Route::crud('region', 'RegionCrudController');

    // Neighborhoob from centris
    Route::crud('neighborhood', 'NeighborhoodCrudController');

    // Offices from centris
    Route::crud('office', 'OfficeCrudController');

    // Regions from centris
    Route::crud('broker', 'BrokerCrudController');

    //----------
    // CONTENT
    //----------

    // Override UserCrudController
    Route::crud('user', 'UserCrudController');

    // Members manager for e-closion
    Route::crud('visit-reports', 'VisitReportCrudController');

    // Neighbohood manager for e-closion
    Route::crud('eclosionneighborhood', 'EclosionNeighborhoodCrudController');
    Route::crud('sitesneighborhood', 'SitesNeighborhoodCrudController');
    Route::crud('sitesneighborhood/{slug}/images', 'SitesNeighborhoodsImageCrudController');

    Route::crud('sitesneighborhoodv2', 'SitesNeighborhoodV2CrudController');

    // Taxe Mutation manager for e-closion
    Route::crud('taxemutation', 'TaxMutationCrudController');

    Route::crud('inscription', 'InscriptionCrudController');
    Route::crud('inscription', 'InscriptionCrudController');

    // SITE INSCRIPTION EXTRAS
    Route::crud('inscription/{mls}/inscriptions_extra', 'InscriptionsExtraCrudController');
    // SITE INSCRIPTION CONTACTS (for reports etc.)
    Route::crud('inscription/{identifier}/contact', 'SiteInscriptionContactCrudController');
    // INSCRIPTION MONTHLY REPORTS
    Route::crud('inscription/{identifier}/monthlyreports', 'MonthlyReportCrudController');
    // Inscription collaboration
    Route::crud('inscriptions_collaboration', 'InscriptionCollaborationCrudController');

    // Billing
    Route::crud('daily-transferts', 'SitesDailyTransfertCrudController');
    Route::crud('monthly-bills', 'SitesMonthlyBillCrudController');

    // Real Estate projects manager for site e-closion
    Route::crud('groupinscriptions', 'InscriptionsGroupCrudController');

    Route::post('medias/{mediamodel}/add', 'MultipleUploadMediaController@upload')->name('multi_upload.add');
    Route::post('medias/{mediamodel}/remove', 'MultipleUploadMediaController@remove')->name('multi_upload.remove');
    Route::post('medias/{mediamodel}/reorder', 'MultipleUploadMediaController@reorder')->name('multi_upload.reorder');
    Route::post('medias/{mediamodel}/rename', 'MultipleUploadMediaController@rename')->name('multi_upload.rename');
    Route::post('medias/{mediamodel}/summernote-upload', 'MultipleUploadMediaController@summerNoteUpload')->name('upload_summernote_medias');

    // Partners manager for e-closion
    Route::crud('partners/categories', 'PartnersCategoryCrudController');
    Route::crud('partners', 'PartnerCrudController');
    // Members manager for e-closion
    Route::crud('teammembers', 'TeammemberCrudController');

    // Authorized domains for API
    Route::crud('apidomains', 'ApidomainCrudController');

    //-- SITE
    Route::crud('site', 'SiteCrudController');
    Route::crud('sitesbrokerscollaborator', 'SitesBrokersCollaboratorCrudController');

    //-- BLOG
    Route::crud('blog/posts', 'BlogPostCrudController');
    Route::crud('blog/posts-v2', 'BlogPostV2CrudController');
    Route::crud('blog/category', 'BlogCategoryCrudController');

    //-- CONTENT BLOCKS
    Route::crud('editableblockpage', 'EditableBlockPageCrudController');
    Route::crud('editableblock', 'EditableBlockCrudController');
    Route::crud('editableblock/neighborhood/{slug}/list', 'EditableBlockCrudController');

    //-- HOMESTAGING
    Route::crud('homestaging', 'HomeStagingCrudController');

    //-- METATAGS
    Route::crud('metatags', 'MetatagCrudController');

    //-- AWARDS
    Route::crud('award', 'AwardCrudController');

    //-- TESTIMONIALS
    Route::crud('testimonials', 'TestimonialsCrudController');
    Route::crud('testimonials-v2', 'TestimonialV2CrudController');

    //-- CONTACT
    Route::crud('contact_inscription', 'ContactInscriptionCrudController');
    Route::crud('contact_alert', 'ContactAlertCrudController');
    Route::crud('contact_general', 'ContactGeneralCrudController');
    Route::crud('contact_evaluation', 'ContactEvaluationCrudController');

    //-- PUBLICATIONS (PUB, ADD' FLYER...)
    Route::crud('publication', 'PublicationCrudController');
    Route::crud('publicationscategory', 'PublicationsCategoryCrudController');

    //-- DOCUMENTS
    Route::crud('document/{document_type}/{identifier}/list', 'DocumentCrudController');

    //-- PROGRAMS
    Route::crud('programs', 'ProgramCrudController');

    //-- HomePage
    Route::crud('home-page', 'HomePageCrudController');
    Route::crud('campaign/{campaign_id}/landing-page', 'LandingPageCrudController');
    Route::crud('campaign', 'CampaignCrudController');
    Route::get('charts/campaign/{campaign_id}', [App\Services\GoogleAnalyticsService::class, 'getCampaignValues'])->name('charts-campaign');

    //-- Synchronize
    Route::post('synchronize/database', 'SiteCrudController@synchronize');

    Route::get('stop-impersonating', function() {
        backpack_user()->stopImpersonating();
        Alert::add('info', 'Vous êtes de nouveau administrateur.')->flash();
        return Redirect::to(url(App::getLocale() . '/admin/site'));
    });
    Route::get('dashboard', [App\Http\Controllers\DashboardController::class, 'index']);
    Route::get('gmb', [App\Http\Controllers\GoogleMyBusinessController::class, 'index']);
    Route::get('gmb/refresh', [App\Http\Controllers\GoogleMyBusinessController::class, 'refresh']);
    Route::crud('blog/posts-v2', 'BlogPostV2CrudController');
    Route::get('import-staging/{site_id?}', [App\Http\Controllers\ImportStagingController::class, 'index'])->name('import-staging');
    Route::get('import-default-posts/{site_id?}', [App\Http\Controllers\ImportDefaultPostsController::class, 'index'])->name('import-default-posts');

    Route::get('change-filter/{filter_site}', function ($locale, $filter_site) {
        set_current_site_filter($filter_site);
        return Redirect::to(url($locale . '/' . config('backpack.base.route_prefix') . '/dashboard'));
    })->name('change_filter');
});
Route::group([
    'prefix' => config('backpack.base.route_prefix', 'admin') . config('lfm.url_prefix'),
    'middleware' => array_merge(
        (array) config('backpack.base.web_middleware', 'web'),
        (array) config('backpack.base.middleware_key', 'admin')
    )],
    function () {
        \UniSharp\LaravelFilemanager\Lfm::routes();
    }
);