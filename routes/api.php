<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
|
| Localization is handled by RouteServiceProvider.php
|
*/
// Option Route for cros origin
// Route::options('/{any}', function () {
//     return 'true';
// })->where('any', '.*');

// # 1 - Eclosion datas
// Users
Route::get('/users', 'Api\UserController@index');
Route::get('/users/{id}', 'Api\UserController@show');

// Blog & BlogV2
Route::get('/blog/posts', 'Api\BlogPostApiController@index');
Route::get('/blog/posts/{post}', 'Api\BlogPostApiController@show');
Route::get('/blog/posts-v2', 'Api\BlogPostV2ApiController@index');
Route::get('/blog/posts-v2/{slug}', 'Api\BlogPostV2ApiController@show');
Route::get('/blog/categories', 'Api\BlogCategoryApiController@index');

// Awards
Route::get('/awards', 'Api\AwardApiController@index');

// Program
Route::get('/programs', 'Api\ProgramApiController@index');

// Editable blocks
Route::get('/editableblocks', 'Api\EditableBlockApiController@index');
Route::get('/editableblocks/{editableblock}', 'Api\EditableBlockApiController@show');

// Home Page
Route::get('/home-page', 'Api\HomePageApiController@index');

// Landing Page
Route::get('/landing-page/{slug}', 'Api\LandingPageApiController@show');

// Testimonials
Route::get('/testimonials', 'Api\TestimonialApiController@index');
Route::get('/testimonials-v2', 'Api\TestimonialV2ApiController@index');

// Teammembers
Route::get('/teammembers', 'Api\TeammemberApiController@index');

// Meta tags
Route::get('/metatags/{key}', 'Api\MetatagApiController@getMetasByKey');

// @deprecated Collaborators replace by Partners
Route::get('/collaborators', 'Api\PartnerApiController@index');
Route::get('/collaborators/categories', 'Api\PartnersCategoryApiController@index');

// Partners
Route::get('/partners', 'Api\PartnerApiController@index');
Route::get('/partners/categories', 'Api\PartnersCategoryApiController@index');

// Homestaging
Route::get('/homestagings', 'Api\HomeStagingApiController@index');

// Get inscription-groups (property_groups in v2)
Route::get('/inscription-groups', 'Api\InscriptionGroupApiController@index');

// Get one inscription-group (property_groups in v2)
Route::get('/inscription-groups/{group}', 'Api\InscriptionGroupApiController@show');

// # 2 - Centris datas
// ## Inscriptions
// Get all
Route::get('/inscriptions', 'Api\InscriptionApiController@index');
//Get All mls
Route::get('/inscriptions/mls/all', 'Api\InscriptionApiController@getAllMls');
// Get addresses
Route::get('/inscriptions/addresses', 'Api\InscriptionApiController@addresses');
// Get max price
Route::get('/inscriptions/maxprice', 'Api\InscriptionApiController@getMaxPrice');
// Get max price
Route::get('/inscriptions/maxpricerental', 'Api\InscriptionApiController@getMaxPriceRental');
// Get all inscriptions sold
Route::get('/inscriptionssold', 'Api\InscriptionsSoldApiController@index');
// Get one by mls
Route::get('/inscriptions/{mls}', 'Api\InscriptionApiController@show');
// Get charactheristics of one inscription by mls
Route::get('/inscriptions/{mls}/characteristics', 'Api\InscriptionsCharacteristicApiController@index');
// Get addenda of one inscription by mls
Route::get('/inscriptions/{mls}/addenda', 'Api\InscriptionsAddendaApiController@index');
// Expenses
Route::get('/inscriptions/{mls}/expenses', 'Api\InscriptionsExpenseApiController@index');
// Get attached members by mls (members associated with one inscriptions to be notified)
Route::get('/inscriptions/{mls}/teammembers', 'Api\TeammemberApiController@index');
// Get brokers by mls (previously members in v2)
Route::get('/inscriptions/{mls}/brokers', 'Api\BrokerApiController@index');
// Get photos by mls
Route::get('/inscriptions/{mls}/photos', 'Api\InscriptionsPhotoApiController@index');
// Get rooms by mls
Route::get('/inscriptions/{mls}/rooms', 'Api\InscriptionsRoomApiController@index');
// Get inscriptions notes by mls
Route::get('/inscriptions/{mls}/notes', 'Api\InscriptionsNoteApiController@index');
// Get renovations by mls
Route::get('/inscriptions/{mls}/renovations', 'Api\InscriptionsRenovationApiController@index');
// Get documents by mls
Route::get('/inscriptions/{mls}/documents', 'Api\InscriptionsDocumentApiController@index');
// Get neighborhood by mls inscription
Route::get('/inscriptions/{mls}/neighborhood', 'Api\InscriptionApiController@getNeighborhoodByMls');
// Get neighborhood by mls inscription
Route::get('/inscriptions/{mls}/pagination', 'Api\InscriptionApiController@pagination');

// Post Statistics
Route::post('/statistics/inscriptions/{mls}/view', 'Api\InscriptionsStatApiController@view');
Route::post('/statistics/inscriptions/{mls}/contact', 'Api\InscriptionsStatApiController@contact');

// ## other
// Brokers
Route::get('/brokers', 'Api\BrokerApiController@index');
Route::get('/brokers/{code}', 'Api\BrokerApiController@show');
// InscriptionOpenHouses
Route::get('/openhouses', 'Api\InscriptionsOpenhouseApiController@index');
Route::get('/inscriptions/{mls}/openhouses', 'Api\InscriptionsOpenhouseApiController@show');
// Neighborhood
// Route::get('/sitesneighborhoods', 'Api\SitesNeighborhoodApiController@index');
Route::get('/neighborhood/{slug}', 'Api\SitesNeighborhoodApiController@show');
Route::get('/neighborhoods/map', 'Api\SitesNeighborhoodApiController@geoJSONAll');
Route::get('/neighborhood/{slug}/map', 'Api\SitesNeighborhoodApiController@geoJSONSingle');

// Neighborhoods v2
// Route::get('/neighborhoodsv2', 'Api\SitesNeighborhoodApiControllerV2@index');
Route::get('/neighborhoodsv2/map', 'Api\SitesNeighborhoodApiControllerV2@geoJSONAll');
Route::get('/neighborhoodsv2/{slug}', 'Api\SitesNeighborhoodApiControllerV2@show');
Route::get('/neighborhoodsv2/{slug}/map', 'Api\SitesNeighborhoodApiControllerV2@geoJSONSingle');

// Municipalities
Route::get('/municipalities', 'Api\MunicipalityApiController@index');

// Get inscriptions types
Route::get('/propertiestypes', 'Api\PropertiestypeApiController@index');

// # 3 - Contact
Route::post('/contact/general', 'Api\ContactApiController@contactGeneral');
// /appointment is duplicate of /property, because some front-end rely on it, do not delete
Route::post('/contact/appointment', 'Api\ContactApiController@contactInscription');
Route::post('/contact/inscription', 'Api\ContactApiController@contactInscription');
Route::post('/contact/evaluation', 'Api\ContactApiController@contactEvaluation');
Route::post('/contact/career', 'Api\ContactApiController@contactCareer');
Route::post('/contact/alert', 'Api\ContactApiController@contactAlert');

// Google My Business
Route::get('/googlemybusiness', 'Api\GoogleMyBusinessController@index');
