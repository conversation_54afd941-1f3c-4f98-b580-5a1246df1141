
<!-- textarea -->
@php
    $pageId = isset($entry) ? $entry->id : hexdec(uniqid());
@endphp
@include('crud::fields.inc.wrapper_start')
    <label>{!! $field['label'] !!}</label>
    @include('crud::fields.inc.translatable_icon')
    <textarea name="{{ $field['name'] }}"
        @include('crud::fields.inc.attributes')
         id="laraberg" hidden>
         {{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? '' }}
    </textarea>
    {{-- HINT --}}
    @if (isset($field['hint']))
        <p class="help-block">{!! $field['hint'] !!}</p>
    @endif
@include('crud::fields.inc.wrapper_end')

@push('crud_fields_styles')
    <link rel="stylesheet" href="{{asset('vendor/laraberg/css/laraberg.css')}}">
    <link rel="stylesheet" href="{{asset('css/laraberg-custom.css')}}">
@endpush

@push('crud_fields_scripts')
    <script src="https://unpkg.com/react@16.8.6/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@16.8.6/umd/react-dom.production.min.js"></script>
    <script src="{{ asset('vendor/laraberg/js/laraberg.js') }}"></script>
    {{-- <script src="{{ asset('js/laraberg.min.js') }}"></script> --}}
    <script type="module" src="{{ asset('vendor/laraberg/js/laraberg-translation.js') }}"></script>
    <script>

        Laraberg.init('laraberg',{ laravelFilemanager: { prefix: '/{{ config("backpack.base.route_prefix") . config("lfm.url_prefix") }}' }, prefix: '/laraberg', pageId: {{ $pageId }} } );
        document.addEventListener("DOMContentLoaded", () => {
            window.wp.data.dispatch('core/blocks').removeBlockTypes(['core/gallery']);

            window.wp.blocks.unregisterBlockStyle( 'core/button', 'outline' );
            window.wp.blocks.unregisterBlockStyle( 'core/button', 'fill' );

            window.wp.blocks.registerBlockStyle( 'core/button', [
                {
                    name: 'fill',
                    label: 'Primaire',
                    isDefault: true,
                },
                {
                    name: 'outline',
                    label: 'Secondaire',
                }
            ]);
        });
    </script>
@endpush
