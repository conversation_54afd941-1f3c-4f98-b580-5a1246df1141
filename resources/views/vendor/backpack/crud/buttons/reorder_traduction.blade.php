@if ($crud->get('reorder.enabled') && $crud->hasAccess('reorder'))
   <div class="btn-group" role="group">
      <button class="btn btn-outline-primary dropdown-toggle" id="btnGroupDrop1" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span class="ladda-label"><i class="la la-arrows"></i> {{ trans('backpack::crud.reorder') }} {{ $crud->entity_name_plural }} </span></button>
      <div class="dropdown-menu" aria-labelledby="btnGroupDrop1" style="">
         @foreach (trans('admin.common.languages') as $key => $language )
         <a class="dropdown-item" href="{{ url($crud->route.'/reorder?lang=' . $key) }}">{{ $language}}</a>
         @endforeach
   </div>
</div>
@endif