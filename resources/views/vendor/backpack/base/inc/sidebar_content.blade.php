{{--   THIS LINES OVERRIDES ORIGINAL FILE:  --}}
<div class="sidebar-logo"></div>
@if(backpack_user()->hasRole('admin'))
    <!-- <li class="header">ADMINISTRATION</li> -->
    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/site') }}"><i class="nav-icon fa fa-desktop"></i> <span>Sites</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/site') }}"><span>Sites</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/sitesbrokerscollaborator') }}"><span>Options des collaborations</span></a></li>
        </ul>
    </li>
    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/'.config('backpack.base.route_prefix', 'admin') . '/user') }}"><i class="nav-icon fa fa-users"></i> <span>Utilisateurs</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/'.config('backpack.base.route_prefix', 'admin') . '/user') }}"> <span>Utilisateurs</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(config('backpack.base.route_prefix', 'admin') . '/role') }}"> <span>Rôles</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(config('backpack.base.route_prefix', 'admin') . '/permission') }}"> <span>Permissions</span></a></li>
        </ul>
    </li>
    <hr/>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/apidomains') }}"><i class="nav-icon fa fa-cogs" aria-hidden="true"></i> <span>Domaines autorisés</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/eclosionneighborhood') }}"><i class="nav-icon fa fa-map-marker"></i> <span>Données de quartiers</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/editableblockpage') }}"><i class="nav-icon fa fa-commenting"></i> <span>Blocs éditables</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/taxemutation') }}"><i class="nav-icon fa fa-diamond"></i> <span>Taxe de mutation</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/metatags') }}"><i class="nav-icon fa fa-bullhorn"></i> <span>Metatags</span></a></li>
    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/monthly-bills') }}"><i class="nav-icon fa fa-dollar"></i> <span>Facturation</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/monthly-bills') }}"> <span>Mensuel</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/daily-transferts') }}"> <span>Quotidient</span></a></li>
        </ul>
    </li>
    <hr>
    <!-- <li class="header">RÉFÉRENCE CENTRIS</li> -->
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/office') }}"><i class="nav-icon fa fa-file-o"></i> <span>Bureaux</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/broker') }}"><i class="nav-icon fa fa-file-o"></i> <span>Courtiers</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/fixedvalue') }}"><i class="nav-icon fa fa-file-o"></i> <span>Valeurs fixes</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/region') }}"><i class="nav-icon fa fa-file-o"></i> <span>Régions</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/municipality') }}"><i class="nav-icon fa fa-file-o"></i> <span>Municipalités</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/neighborhood') }}"><i class="nav-icon fa fa-file-o"></i> <span>Quartiers</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/inscription') }}"><i class="nav-icon fa fa-file-o"></i> <span>Inscriptions</span></a></li>
@endif
@if(backpack_user()->hasRole('client'))
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale() . config('backpack.base.home_link')) }}"><i class="nav-icon fa icon-control-panel"></i> {{ trans('backpack::base.dashboard') }}</a></li>
    <hr />
    @if (backpack_user()->hasPermissionTo('custom_home_page'))
        @if (!backpack_user()->sites()->first()->homePage)
            <li class='nav-item'><a class='nav-link' href='{{ url(App::getLocale(). '/admin/home-page/create') }}'><i class='nav-icon fa icon-inscriptions'></i> {{ __('admin.sidebar.homepage') }}</a></li>
        @else
            <li class='nav-item'><a class='nav-link' href='{{ url(App::getLocale(). '/admin/home-page/' . backpack_user()->sites()->first()->homePage->id . '/edit') }}'><i class='nav-icon la la-home'></i> {{ __('admin.sidebar.homepage') }}</a></li>
        @endif
    @endif

    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/editableblock') }}"><i class="nav-icon fa icon-contents"></i> <span>{{ __('admin.sidebar.pages') }}</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/teammembers') }}"><i class="nav-icon fa icon-team"></i> <span>{{ __('admin.sidebar.team_members') }}</span></a></li>
    <li class='nav-item nav-dropdown'>
        @if (backpack_user()->hasPermissionTo("blogpostv2"))
            <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/blog/posts-v2') }}"><i class="nav-icon fa icon-blog "></i> <span>{{ __('admin.sidebar.blog') }}</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        @else
            <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/blog/posts') }}"><i class="nav-icon fa icon-blog "></i> <span>{{ __('admin.sidebar.blog') }}</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        @endif
        <ul class="nav-dropdown-items">
            @if (backpack_user()->hasPermissionTo("blogpostv2"))
                <li class='nav-item'><a class='nav-link' href='{{ url(App::getLocale().'/admin/blog/posts-v2')  }}'><span>{{ __('admin.sidebar.articles') }}</span></a></li>
            @endif
            @if (!backpack_user()->hasPermissionTo("blogpostv2"))
                <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/blog/posts') }}"><span>{{ __('admin.sidebar.articles') }}</span></a></li>
            @endif
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/blog/category') }}"> <span>{{ __('admin.sidebar.categories') }}</span></a></li>
        </ul>
    </li>
    @if (backpack_user()->hasPermissionTo("testimonials_v2"))
        <li class='nav-item'><a class='nav-link' href='{{ url(App::getLocale().'/admin/testimonials-v2') }}'><i class='nav-icon fa icon-testimonies'></i><span> {{ __('admin.sidebar.testimonials') }}</span></a></li>
    @endif
    @if (!backpack_user()->hasPermissionTo("testimonials_v2"))
        <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/testimonials') }}"><i class="nav-icon fa icon-testimonies"></i><span>{{ __('admin.sidebar.testimonials') }}</span></a></li>
    @endif
    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/partners') }}"><i class="nav-icon fa icon-partners"></i><span>{{ __('admin.sidebar.partners') }}</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/partners') }}"> <span>{{ __('admin.sidebar.partner_list') }}</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/partners/categories') }}"> <span>{{ __('admin.sidebar.categories') }}</span></a></li>
        </ul>
    </li>

    <hr />

    @if (backpack_user()->isImpersonating() === true)
        <li class='nav-item nav-dropdown'>
            <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/inscription') }}"><i class="nav-icon fa icon-inscriptions"></i> <span>{{ __('admin.sidebar.inscriptions') }}</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
            <ul class="nav-dropdown-items">
                <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/inscription') }}"> <span>{{ __('admin.sidebar.centris_inscriptions') }}</span></a></li>
                <li class='nav-item impersonating'><a class='nav-link' href="{{ url(App::getLocale().'/admin/inscriptions_collaboration') }}"> <span>{{ __('admin.sidebar.collaborators') }}</span><span class="star-prestige"></span></a></li>
            </ul>
        </li>
    @else
        <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/inscription') }}"><i class="nav-icon fa icon-inscriptions"></i> <span>{{ __('admin.sidebar.inscriptions') }}</span></a></li>
    @endif

    @if (backpack_user()->hasPermissionTo("sitesneighborhoodv2"))
        <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/sitesneighborhoodv2') }}"><i class="nav-icon fa icon-neighborhoods"></i> <span>{{ __('admin.sidebar.neighborhoods') }} (v2)</span><span class="star-prestige"></span></a></li>
    @elseif (backpack_user()->hasPermissionTo("sitesneighborhood"))
        <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/sitesneighborhood') }}"><i class="nav-icon fa icon-neighborhoods"></i> <span>{{ __('admin.sidebar.neighborhoods') }}</span><span class="star-prestige"></span></a></li>
    @endif

    <li class='nav-item'><a class='nav-link' id="li_inscriptions_groups" href="{{ url(App::getLocale().'/admin/groupinscriptions') }}"><i class="nav-icon fa icon-projects"></i> <span>{{ __('admin.sidebar.inscription_groups') }}</span><span class="star-prestige"></span></a></li>

    <hr />

    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/award') }}"><i class="nav-icon fa icon-distinctions"></i><span>{{ __('admin.sidebar.awards') }}</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/homestaging') }}"><i class="nav-icon fa icon-homestaging"></i> <span>{{ __('admin.sidebar.home_stagings') }}</span></a></li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/programs') }}"><i class="nav-icon fa icon-featured"></i><span>{{ __('admin.sidebar.programs') }}</span><span class="star-prestige"></span></a></li>

    <hr />

    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/contact_general') }}"><i class="nav-icon fa icon-contact"></i><span>{{ __('admin.sidebar.contact') }}</span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/contact_general') }}"><span>{{ __('admin.sidebar.contact_general') }}</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/contact_inscription') }}"><span>{{ __('admin.sidebar.contact_inscription') }}</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/contact_alert') }}"><span>{{ __('admin.sidebar.contact_alert') }}</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/contact_evaluation') }}"><span>{{ __('admin.sidebar.contact_evaluation') }}</span></a></li>
        </ul>
    </li>
    {{-- <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/monthlyreports') }}"><i class="nav-icon fa fa-envelope-open"></i> <span>Rapports mensuel</span></a></li> --}}
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/visit-reports') }}"><i class="nav-icon fa icon-reports"></i><span>{{ __('admin.sidebar.visit_reports') }}</span><span class="star-prestige"></span></a></li>
    <li class='nav-item nav-dropdown'>
        <a class='nav-link nav-dropdown' href="{{ url(App::getLocale().'/admin/publication') }}"><i class="nav-icon fa icon-publications"></i><span>{{ __('admin.sidebar.publication') }}</span> <span class="star-prestige"></span><i class="nav-icon fa fa-angle-right pull-right"></i></a>
        <ul class="nav-dropdown-items">
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/publication') }}"> <span>{{ __('admin.sidebar.publication') }}</span></a></li>
            <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/publicationscategory') }}"> <span>{{ __('admin.sidebar.categories') }}</span></a></li>
        </ul>
    </li>
    <li class='nav-item'><a class='nav-link' href="{{ url(App::getLocale().'/admin/metatags') }}"><i class="nav-icon fa fa-file-o"></i> <span>{{ __('admin.sidebar.metatags') }}</span></a></li>
    @if (backpack_user()->isImpersonating() === true)
        <li class='nav-item impersonating'><a class='nav-link' href="{{ url(App::getLocale().'/admin/import-staging', current_site_id()) }}"><i class="nav-icon fa fa-refresh"></i> <span>{{ __('admin.sidebar.import-staging') }}</span></a></li>
    @endif
    @if (backpack_user()->isImpersonating() === true)
        <li class='nav-item impersonating'><a class='nav-link' href="{{ url(App::getLocale().'/admin/import-default-posts', current_site_id()) }}"><i class="nav-icon
        fa fa-plus"></i> <span>{{ __('admin.sidebar.import-default-posts') }}</span></a></li>
    @endif

    {{--  custom javascript thing to hightlight "inscriptions" or "  projets immobiliers" on documents, depending from where they are accessed (inscriptions or inscription_groups respectively)  --}}
    <script>
        var linkInscriptions = document.getElementById("li_inscriptions");
        var linkInscriptionsGroups = document.getElementById("li_inscriptions_groups");
        var path = window.location.pathname;

        if(path.includes('document')) {
            if(path.includes('inscription')) {
                linkInscriptions.classList.add('active');
            } else {
                linkInscriptionsGroups.classList.add('active');
            }
        }
    </script>
@endhasrole
{{--  END OVERRIDES  --}}
