<?php 
	// TODO: Move to external file and share with all email templates
    $width          = "790px";
	$font 			= "'Open Sans', 'Arial', 'Verdana'";
	$primaryColor	= $mail_color;
	$txtColor		= "#2A2B2D";

	$stl_body 		= "margin: 0;";
    $stl_mainDiv 	= "padding:30px 10px";
	$stl_content 	= "width: 100%; background-color: white; font-family: $font;  padding:20px 10px; border: 10px solid $primaryColor;";
	$stl_h1 		= "margin: 0 15px 35px; color: $primaryColor; font-family: $font; font-size: 17px; font-weight: bold; letter-spacing: 1.31px; line-height: 23px; text-transform: uppercase;";
	$stl_h2 		= "margin: 0 15px 8px; color: $primaryColor; font-family: $font; font-size: 14px; font-weight: bold; letter-spacing: 1px; line-height: 18px;";
	$stl_h3 		= "margin: 0 15px 8px; color: $primaryColor; font-family: $font; font-size: 16px; font-weight: bold; letter-spacing: 1.23px; line-height: 22px; text-transform: uppercase;";
	$stl_p1 		= "margin: 0 15px 14px; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 1.08px; line-height: 19px;";
	$stl_p2 		= "margin: 0 15px; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 0; line-height: 19px;";
	$stl_table 		= "width: 100%; margin: 50px 0 0; padding-top: 40px; border-top: 2px solid $primaryColor; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 1.08px; line-height: 19px;";
	$stl_td         = "color: $primaryColor; font-size: 14px; vertical-align: top;";
	$stl_td_strong  = "width: auto; padding: 0px 40px 8px 12px; color: $primaryColor; font-size: 14px; font-weight: bold; vertical-align: top;";
    $stl_td_right	= "text-align: right; border-top: 2px solid $primaryColor;";
	$stl_img 		= "height: 70px; padding-top:20px;";  
?>

<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
        <title>{{ $contact->subject }}</title>
		<style>
			body { padding: 15px 15px; }
			a { color: <?=$primaryColor?>; word-break: break-word; }
			@media (min-width: 768px) { 
				body { padding: 35px; }
			}
		</style>
    </head>

    <body style="<?=$stl_body?>">
        <div style="<?= $stl_mainDiv?>">
            <table id="content" style="<?=$stl_content?>">
                <tr>
                    <td colspan="5">
                        <h1 style="<?=$stl_h1?>">{{ $contact->subject }}</h1>
                    </td>
                </tr>

                <tr>
                    <td colspan="5">
                        <p style="<?=$stl_p1?>">{{ $contact->comments }}</p>
                    </td>
                </tr>

                
                <tr style="<?=$stl_table?> border: 0px none; margin-top: 0px; width: auto; padding-top: 0px;">
                    <td colspan="2" style="<?=$stl_td_strong?>">@lang('emails/contact.name')</td>
                    <td>{{  $contact->firstname.' '.$contact->lastname }}</td>
                </tr>

                <tr>
                    <td colspan="2" style="<?=$stl_td_strong?>">@lang('emails/contact.email')</td>
                    <td><a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a></td>
                </tr>

                @if($contact->phone)
                    <tr>
                        <td colspan="2" style="<?=$stl_td_strong?>">@lang('emails/contact.phone')</td>
                        <td>
                        <p>
                            {{ $contact->phone }}
                            @if($contact->phone_ext !== null)
                                # {{ $contact->phone_ext }}
                            @endif
                        </p>
                        </td>
                    </tr>
                @endif

                <tr>
                    <td>
                        <p style="padding-bottom:40px;"></p>
                    </td>
                </tr>
        
                <tr>
                    <td colspan="5" style="<?=$stl_td_right?>">
                        @if($logo_path != '')
                            <img id="logo" style="<?=$stl_img?>" src="{{ $logo_path }}" alt="logo">
                        @endif
                    </td>
                </tr>
            </table>
        </div>
    </body>
</html>
