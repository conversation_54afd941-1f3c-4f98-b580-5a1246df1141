<?php 
	// TODO: Move to external file and share with all email templates
    $width          = "790px";
	$font 			= "'Open Sans', 'Arial', 'Verdana'";
	$primaryColor	= $mail_color;
	$txtColor		= "#2A2B2D";

	$stl_body 		= "margin: 0;";
    $stl_mainDiv 	= "padding:30px 10px";
	$stl_content 	= "width: 100%; background-color: white; padding:20px 10px; border: 10px solid $primaryColor;";
	$stl_h1 		= "margin: 0 15px 35px; color: $primaryColor; font-family: $font; font-size: 17px; font-weight: bold; letter-spacing: 1.31px; line-height: 23px; text-transform: uppercase;";
	$stl_h2 		= "margin: 0 15px 8px; color: $primaryColor; font-family: $font; font-size: 14px; font-weight: bold; letter-spacing: 1px; line-height: 18px;";
	$stl_h3 		= "margin: 0 15px 8px; color: $primaryColor; font-family: $font; font-size: 16px; font-weight: bold; letter-spacing: 1.23px; line-height: 22px; text-transform: uppercase;";
	$stl_li 		= "color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 1.08px; line-height: 19px;";
	$stl_p1 		= "margin: 0 15px 14px; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 1.08px; line-height: 19px;";
	$stl_p2 		= "margin: 0 15px; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 0; line-height: 19px;";
	$stl_table 		= "width: 100%; margin: 50px 0 0; padding-top: 40px; border-top: 2px solid $primaryColor; color: $txtColor; font-family: $font; font-size: 14px; letter-spacing: 1.08px; line-height: 19px;";
	$stl_td         = "color: $primaryColor; font-size: 14px; vertical-align: top;";
	$stl_td_strong  = "width: auto; padding: 0px 40px 8px 12px; color: $primaryColor; font-size: 14px; font-weight: bold; vertical-align: top;";
    $stl_td_right	= "text-align: right;";
	$stl_img 		= "height: 70px;"; 
    $stl_border		= "vertical-align: top; padding-top: 40px; border-top: 2px solid $primaryColor;";
?>

<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
        <title>@lang('emails/contact.alert.message')</title>
		<style>
			body { padding: 15px 15px; }
			a { color: <?=$primaryColor?>; word-break: break-word; }
			@media (min-width: 768px) { 
				body { padding: 35px; }
			}
		</style>
    </head>

    <body style="<?=$stl_body?>">
        <div style="<?= $stl_mainDiv?>">
            <table id="content" style="<?=$stl_content?>">
                <tr>
                    <td colspan="5">
                        <h1 style="<?=$stl_h1?>">@lang('emails/contact.alert.message')</h1>
                    </td>
                </tr>

                <tr>
                    <td>
                    <h2 style="<?=$stl_h2?>">@lang('emails/contact.alert.cities')</h2>
                    <ul>
                            @if (!empty($infos->cities))
                                @foreach(json_decode($infos->cities) as $city)
                                    <li style="<?=$stl_li?>">{{ $city }}</li>
                                @endforeach
                            @else
                                <li style="<?=$stl_li?>">Aucune ville précisée</li>
                            @endif
                        </ul>
                    </td>
                </tr>

            

                <tr>
                    <td>
                        <h2 style="<?=$stl_h2?>">@lang('emails/contact.alert.property_types')</h2>
                        <ul>
                            @if (!empty($infos->property_types))
                                @foreach(json_decode($infos->property_types) as $type)
                                    <li style="<?=$stl_li?>">{{ $type }}</li>
                                @endforeach
                            @else
                                <li style="<?=$stl_li?>">Aucun type de propriété précisée</li>
                            @endif
                        </ul>
                    </td>
                </tr>

            

                @if($infos->other != null)

                <tr>
                    <td>
                        <h2 style="<?=$stl_h2?>">@lang('emails/contact.alert.other')</h2>
                        <p style="<?=$stl_p1?>">{{ $infos->other }}</p>
                    </td>
                </tr>
                @endif
            

                @if($infos->budget != null)
                <tr>
                    <td>
                        <h2 style="<?=$stl_h2?>">@lang('emails/contact.alert.budget')</h2>
                        <p style="<?=$stl_p1?>">{{ $infos->budget }}</p>
                    </td>
                </tr>
                @endif
            
                <tr>
                    <td>
                    <h2 style="<?=$stl_h2?>">@lang('emails/contact.alert.perso_infos')</h2>
                    </td>
                </tr>

                <tr>
                    <td>
                    <p style="<?=$stl_p1?>"><strong>@lang('emails/contact.name'): </strong> {{ $infos->firstname.' '.$infos->lastname }}</p>
                    <p style="<?=$stl_p1?>"><strong>@lang('emails/contact.phone'): </strong> {{ $infos->phone.($infos->phone_ext != null ? ' Ext. '.$infos->phone_ext : '') }}</p>
                    <p style="<?=$stl_p1?>"><strong>@lang('emails/contact.email'): </strong> {{ $infos->email_alert }}</p>
                    </td>
                </tr>

                    
                <tr style="<?=$stl_table?>">
                    <td colspan="5" style="<?=$stl_border?>">
                        <h3 id="name" style="<?=$stl_h3?>">{{ $infos['site_name'] }}</h3>
                        <p id="phone" style="<?=$stl_p2?>"></p>
                        <p id="email" style="<?=$stl_p2?>"></p>
                    </td>
                </tr>
                <tr>
                    <td colspan="5" style="<?=$stl_td_right?>">
                        @if($logo_path != '')
                            <img id="logo" style="<?=$stl_img?>" src="{{ $logo_path }}" alt="logo">
                        @endif
                        </td>
                </tr>
            </table>
        </div>
    </body>
</html>
