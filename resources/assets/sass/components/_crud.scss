    .impersonating {
        label {
            color: #E4B448 !important;
        }
    }
// CRUD tables
#crudTable_wrapper {
    margin-top: 10px;
    
    // Remove negative margins for auto-generated table elements
    > div { 
        margin: 0px; 
        > .col-sm-12 { padding: 0px; }
    }

    // Basic table styling
    table {
        margin-bottom: 0 !important;

        thead {
            background-color: #F6F7F8;
            color: #8898AA;
            
            th {
                padding: 15px;
                border: 0px none;
                border-top: 1px solid $table-border-color;
                border-bottom: 1px solid $table-border-color;
                color: #8898AA;
                font-size: 11px;
                font-weight: 600;
                letter-spacing: 1px;
                text-transform: uppercase;
            }
            th::before, th::after {
                display: none;
            }
            th:first-child {
                padding-left: 30px;
            }
        }
        
        tbody {
            tr {
                transition: $mainTransition;
                background-color: white;
            }
            tr:hover { background-color: $sidebar-hover-color; }
            td {
                padding: 15px;
                vertical-align: middle;
                border-top: 0px none;
                border-bottom: 1px solid #E9ECEF;
                font-size: 13px;
                a { color: $green; }
                &.dataTables_empty { text-align: center; }
                div.preview.show {
                    display: inline-block !important;
                }
            }
            td::before { display: none; }
            td:first-child { padding-left: 30px; }
            td:last-child { padding-right: 30px; }
        }

        td:last-child, th:last-child { text-align: right; }
        tfoot { display: none; }
    }

    // Bottom controls and pagination
    > .row:last-child {
        align-items: center;
        margin: 0 !important;
        padding: 30px !important;

        .pagination {
            margin: 0 !important;
            display: flex;
            justify-content: flex-end;

            li { 
                a { 
                    color: #444;
                    font-weight: 600;
                    border: 1px solid #E1E1E1;
                    background-color: white;
                    border-radius: 0;
                    transition: $mainTransition;
                }

                &:first-child a { border-radius: 5px 0 0 5px; }
                &:last-child a { border-radius: 0 5px 5px 0; }

                &.active a, &:hover a {
                    background-color: $green;
                    border: 1px solid $green;
                    color: white !important;
                }

                &.active a:hover { background-color: $dark-green; }
            }	
        }
    }

    // Table footer label
    .dataTables_length label { 
        font-size: 13px; 
        color: rgba(107, 112, 118, 0.7); 
        font-weight: normal !important; 
        select { margin-right: 15px; }
    }
}

#datatable_search_stack { 
    position: relative;
    margin-left: auto;
    width: max-content;

    #crudTable_filter {
        position: relative;
        label { margin: 0 !important; }
        &::before {
            position: absolute;
            right: 12px;
            top: 8px;
            content: "";
            background: url("../images/search-solid.svg") no-repeat;
            width: 15px;
            height: 15px;
        }
        input {
            width: auto;
            min-width: 250px;
            border-radius: 18px;
            border: 1px solid #CCC;
            height: 35px;
            -webkit-box-shadow: none;
            box-shadow: none;
        }
    }

    @media (max-width: $xs-small) {
        margin-left: 0;
    }
}

#crudTable_length {
    select {
        height: 35px;
    }
}