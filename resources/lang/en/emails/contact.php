<?php
return [
    'email'       => 'Email',
    'name'        => 'Name',
    'phone'       => 'Telephone',
    'address'     => 'Address',
    'yes'         => 'Yes',
    'no'          => 'No',
    'comments'    => 'Comments',
    'inscription' => [
        'subject' => [
            'no-appointment' => 'Request for information - #:mls - :address',
            'appointment'    => 'Appointment request - #:mls - :address',
        ],
        'appointment_time' => 'Appointment',
    ],
    'alert' => [
        'subject'        => 'Real estate alert request',
        'cities'         => 'Cities',
        'message'        => 'Real estate alert request',
        'property_types' => 'Property types',
        'other'          => 'Other',
        'budget'         => 'Budget',
        'perso_infos'    => 'Personal information',
    ],
    'career' => [
        'subject'           => 'Job application - :job',
        'available_from'    => 'Available from',
        'message_candidate' => 'Candidate\'s message',
        'interested_in'     => 'Interested by the position',
        'name'              => 'Name',
        'address'           => 'Address',
        'city'              => 'City',
        'email_candidate'   => 'Candidate\'s email',
        'phone'             => 'Telephone'
    ],
    'evaluation' => [
        'isTooFar'                => 'Request out of zone',
        'subject'                 => 'Property assessment request',
        'title'                   => 'Request for assessment of the property\'s market value',
        'address'                 => 'Address',
        'about_property'          => 'About the property',
        'year'                    => 'Construction year',
        'reason'                  => 'Why are you selling?',
        'professional_evaluation' => 'Accredited assessment carried out',
        'purpose'                 => 'Purpose of the process',
        'delay_to_move'           => 'Ideal moving time',
        'delay_to_sold'           => 'Delay before sale',
        'size'                    => 'Living area',
        'dimension'               => 'Lot area',
        'renovations'             => 'Renovations',
        'bedroom'                 => 'Bedrooms',
        'bathroom'                => 'Bathrooms',
        'parking'                 => 'Parking',
        'garage'                  => 'Garages',
        'basement'                => 'Basement',
        'swimming_pool'           => 'Pool',
        'street_view_link'        => 'Street view link',
        'property_more_info'      => 'More information',
        'perso_infos'             => 'Personal information',
        'link'                    => 'Link',
        'join'                    => 'Contact information',
    ],
    'general' => [
        'subject'   => 'Request for general information',
        'recaptcha' => 'Captcha\'s value are invalid. Erreur : :google-message',
    ],
    'confirmation' => [
        'general' => [
            'subject' => 'Request for information - :site',
            'title'   => 'Your request for information has been sent',
            'success' => 'Your request for information has been sent',
            'text'    => 'Hello, <br><br>We have received your request. We will contact you as soon as possible. <br><br>Thank you for your interest and see you soon.'
        ],
        'alert' => [
            'subject' => 'Real estate alert',
            'title'   => 'Your real estate alert request has been sent!',
            'success' => 'Your real estate alert request has been sent!',
            'text'    => 'Hi :name, <br><br>A broker will contact you as soon as a property meets your criteria. <br><br>Thank you for your trust and see you soon.',
        ],
        'evaluation' => [
            'subject' => 'Free assessment of the market value of your property',
            'title'   => 'Your free property assessment request',
            'success' => 'Your free property assessment request has been sent',
            'text'    => 'Hi :name, <br><br>We have received your request for a free assessment of the market value of your property. A broker will contact you shortly. <br><br>Thank you for your trust and see you soon.',
        ],
        'inscription' => [
            'appointment' => [
                'subject' => "Appointment request - :address",
                'title'   => 'Your request for an appointment',
                'success' => 'Your request for an appointment has been sent',
                'text'    => 'Hi :name, <br><br>:broker has received your property request for the :address, and will be in touch with you shortly. <br><br>Thank you for your time and see you soon.'
            ],
            'no-appointment' => [
                'subject' => 'Request for information - :address',
                'title'   => 'Your request for information',
                'success' => 'Your request for information has been sent',
                'text'    => 'Hi :name, <br><br>:broker has received your request for information for the :address, and will be in touch with you shortly. <br><br>Thank you for your time and see you soon.'
            ]
        ],
        'career' => [
            'subject' => 'Your job request has been sent!',
            'title'   => 'Your job request application,',
            'success' => 'Your job request application has been sent',
            'text'    => 'Hi :name, <br><br>Your application for the :job position has been sent to :broker. <br><br>Thank you for your interest and we\'ll be in touch with you shortly.'
        ]
    ]
];
