<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class ContactConfirmationMailer extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The contact infos.
     *
     */
    public $infos;

     /**
     * The logo of the website.
     *
     * @var string
     */
    public $logo_path;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Array $infos, String $logo_path)
    {
        $this->infos = $infos;
        $this->logo_path = $logo_path;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
                ->from(preg_replace('/\s+/', '', trim($this->infos['email_from'])))
                ->subject($this->infos['subject'])
                ->view('emails.contact_confirmation')
                ->with([
                    'infos' => $this->infos,
                ]);
    }
}
