<?php

namespace App\Mail;

use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Bus\Queueable;
use App\Models\ContactGeneral;

class ContactGeneralMailer extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The contact instance.
     *
     * @var ContactGeneral
     */
    public $contact;

    /**
     * The logo of the website.
     *
     * @var string
     */
    public $logo_path;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(ContactGeneral $contact, String $logo_path)
    {
        $this->contact = $contact;
        $this->logo_path = $logo_path;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
                ->from(config('mail.from.address'), config('mail.from.name'))
                ->replyTo(preg_replace('/\s+/', '', trim($this->contact->email)), $this->contact->firstname.' '.$this->contact->lastname)
                ->subject(__('emails/contact.general.subject'))
                ->view('emails/contact_general')
                ->with([
                    'mail_color' => isset($this->contact->site->mail_color) ? $this->contact->site->mail_color : '#18355E'
                ]);
    }
}
