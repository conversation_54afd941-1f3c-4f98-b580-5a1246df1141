<?php

namespace App\Console\Commands;

use App\Models\Site;
use App\Models\SitesNeighborhood;
use App\Models\SitesNeighborhoodV2;
use App\Models\SitesNeighborhoodsImage;
use App\Models\SitesNeighborhoodsImageV2;
use Illuminate\Console\Command;

class ConvertNeighborhoodV2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'eclosion:import-site-neighborhood-to-v2 {--fk_site_id= : Site id }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert v1 site neighborhoods to v2';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('-- Import sites_neighborhoods to SitesNeighborhoodV2 started.');
        $this->newLine();
        
        $fk_site_id = $this->option('fk_site_id');
        
        if (!isset($fk_site_id) || empty($fk_site_id)) {
            $fk_site_id = $this->ask('What is the id of the chosen site?');
        }
        
        $site = Site::find($fk_site_id);
        if (!isset($site)) {
            $this->error('No site found with the id ' . $fk_site_id);
            return Command::FAILURE;
        }
        
        if ($this->confirm('The chosen site is: ' . $site->name . '. Do you wish to continue?')) {
            $this->info('Site validated. Import can begin.');
            $this->newLine();
            
            $this->info('-- Fetching Sites Neighborhoods');
            $this->newLine();
            $query = SitesNeighborhood::where('fk_site_id', $fk_site_id);
            $nbSites = $query->count();
            $sitesNeighborhoods = $query->get();
            $this->info('-- Found ' . $nbSites . ' SiteNeighborhood(s)');

            $createdNeighborhoods = [];

            foreach ($sitesNeighborhoods as $sitesNeighborhood) {
                $this->info('--- Processing SiteNeighborhood: ' . $sitesNeighborhood->id . ' - ' . $sitesNeighborhood->name);
                
                $frNeighborhood = $this->createV2Neighborhood($sitesNeighborhood, 'fr');
                if ($frNeighborhood) {
                    $createdNeighborhoods[$sitesNeighborhood->id]['fr'] = $frNeighborhood;
                    $this->info('---- Created French version: ID ' . $frNeighborhood->id);
                }
                
                $enNeighborhood = $this->createV2Neighborhood($sitesNeighborhood, 'en');
                if ($enNeighborhood) {
                    $createdNeighborhoods[$sitesNeighborhood->id]['en'] = $enNeighborhood;
                    $this->info('---- Created English version: ID ' . $enNeighborhood->id);
                }
                
                // Link French and English versions together
                if (isset($createdNeighborhoods[$sitesNeighborhood->id]['fr']) && 
                    isset($createdNeighborhoods[$sitesNeighborhood->id]['en'])) {
                    
                    $frRecord = $createdNeighborhoods[$sitesNeighborhood->id]['fr'];
                    $enRecord = $createdNeighborhoods[$sitesNeighborhood->id]['en'];
                    
                    // Link them to each other
                    $frRecord->fk_sites_neighborhood_id = $enRecord->id;
                    $enRecord->fk_sites_neighborhood_id = $frRecord->id;
                    
                    $frRecord->save();
                    $enRecord->save();
                    
                    $this->info('---- Linked FR/EN versions together');
                    
                    $this->copyImagesToLanguageVersion($frRecord, $enRecord);
                }
            }

        } else {
            $this->warn('Invalid site found with the id given.');
            $this->warn('Command aborted by the user.');
            return Command::INVALID;
        }
        
        $this->newLine();
        $this->info('The import was successful!');
        return Command::SUCCESS;
    }

    /**
     * Create a V2 neighborhood record for a specific language
     *
     * @param SitesNeighborhood $v1Neighborhood
     * @param string $language
     * @return SitesNeighborhoodV2|null
     */
    private function createV2Neighborhood(SitesNeighborhood $v1Neighborhood, string $language)
    {
        try {
            // Get language-specific content
            $description = $language === 'fr' ? $v1Neighborhood->description_fr : $v1Neighborhood->description_en;
            $metaTitle = $language === 'fr' ? $v1Neighborhood->meta_title_fr : $v1Neighborhood->meta_title_en;
            $metaDescription = $language === 'fr' ? $v1Neighborhood->meta_description_fr : $v1Neighborhood->meta_description_en;
            $editableBlocks = $language === 'fr' ? $v1Neighborhood->editableblocks_fr : $v1Neighborhood->editableblocks_en;
            
            // Skip if no content for this language
            if (empty($description) && empty($metaTitle) && empty($metaDescription)) {
                $this->warn('---- Skipping ' . strtoupper($language) . ' version - no content found');
                return null;
            }
            
            // Convert description to Laraberg format if needed
            $larabergDescription = $this->convertToLaraberg($this->processJsonField($description));

            $pointsOfInterest = $this->processPointsOfInterest($v1Neighborhood->points_of_interest, $language);
            $processedEditableBlocks = $this->processJsonField($editableBlocks);
            
            // Create V2 record
            $v2Neighborhood = SitesNeighborhoodV2::create([
                'fk_site_id' => $v1Neighborhood->fk_site_id,
                'fk_eclosion_neighborhood_id' => $v1Neighborhood->fk_eclosion_neighborhood_id,
                'language' => $language,
                'name' => $v1Neighborhood->name,
                'slug' => $v1Neighborhood->slug,
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'description' => $larabergDescription,
                'video_url' => $v1Neighborhood->video_url,
                'header_image' => $v1Neighborhood->header_image,
                'points_of_interest' => $pointsOfInterest,
                'editableblocks' => $processedEditableBlocks,
                'geometry_type' => $v1Neighborhood->geometry_type,
                'coordinates' => $v1Neighborhood->coordinates,
                'published' => true,
                'no_index' => false,
            ]);
            
            // Copy images from V1 to V2 (only for the first language to avoid duplicates)
            if ($language === 'fr') {
                $this->copyNeighborhoodImages($v1Neighborhood, $v2Neighborhood);
            }
            
            return $v2Neighborhood;
            
        } catch (\Exception $e) {
            $this->error('---- Error creating ' . strtoupper($language) . ' version: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Copy images from French version to English version
     *
     * @param SitesNeighborhoodV2 $frNeighborhood
     * @param SitesNeighborhoodV2 $enNeighborhood
     * @return void
     */
    private function copyImagesToLanguageVersion(SitesNeighborhoodV2 $frNeighborhood, SitesNeighborhoodV2 $enNeighborhood)
    {
        try {
            // Get all images from French version
            $frImages = $frNeighborhood->images;
            
            if ($frImages->count() > 0) {
                $this->info('---- Copying ' . $frImages->count() . ' image(s) to EN version');
                
                foreach ($frImages as $frImage) {
                    SitesNeighborhoodsImageV2::create([
                        'fk_site_neighborhood_id' => $enNeighborhood->id,
                        'path' => $frImage->path,
                        'title' => $frImage->title,
                        'published' => $frImage->published,
                    ]);
                }
                
                $this->info('---- Successfully copied images to EN version');
            }
            
        } catch (\Exception $e) {
            $this->error('---- Error copying images to EN version: ' . $e->getMessage());
        }
    }

    /**
     * Copy images from V1 neighborhood to V2 neighborhood
     *
     * @param SitesNeighborhood $v1Neighborhood
     * @param SitesNeighborhoodV2 $v2Neighborhood
     * @return void
     */
    private function copyNeighborhoodImages(SitesNeighborhood $v1Neighborhood, SitesNeighborhoodV2 $v2Neighborhood)
    {
        try {
            // Get all images from V1 neighborhood
            $v1Images = $v1Neighborhood->images;
            
            if ($v1Images->count() > 0) {
                $this->info('---- Copying ' . $v1Images->count() . ' image(s)');
                
                foreach ($v1Images as $v1Image) {
                    SitesNeighborhoodsImageV2::create([
                        'fk_site_neighborhood_id' => $v2Neighborhood->id,
                        'path' => $v1Image->path,
                        'title' => $v1Image->title,
                        'published' => $v1Image->published ?? true,
                    ]);
                }
                
                $this->info('---- Successfully copied all images');
            } else {
                $this->info('---- No images to copy');
            }
            
        } catch (\Exception $e) {
            $this->error('---- Error copying images: ' . $e->getMessage());
        }
    }

    private function processPointsOfInterest($pointsOfInterest, $language)
    {
        if (empty($pointsOfInterest)) {
            return null;
        }
        
        // Process the JSON field first
        $processedData = $this->processJsonField($pointsOfInterest);
        
        if (!is_array($processedData)) {
            return $processedData;
        }
        
        $result = [];
        
        foreach ($processedData as $item) {
            if (is_array($item)) {
                $languageKey = 'name_' . $language;
                
                if (isset($item[$languageKey])) {
                    $result[] = [
                        'name' => $item[$languageKey]
                    ];
                }
            }
        }
        
        return $result;
    }

    private function processJsonField($jsonField)
    {
        if (empty($jsonField)) {
            return null;
        }
        
        // If it's already an array, return as is
        if (is_array($jsonField)) {
            return $jsonField;
        }
        
        // If it's a string, try to decode it
        if (is_string($jsonField)) {
            $decoded = json_decode($jsonField, true);
            
            // If decoding failed, return the original string
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $jsonField;
            }
            
            // Return the decoded array
            return $decoded;
        }
        
        // For any other type, return as is
        return $jsonField;
    }

    /**
     * Convert HTML content to Laraberg format
     *
     * @param string|null $htmlContent
     * @return string
     */
    private function convertToLaraberg($htmlContent)
    {
        if (empty($htmlContent)) {
            return '';
        }
        
        // Basic conversion: wrap HTML content in Gutenberg paragraph blocks
        // You might need to adjust this based on your content structure
        $cleanHtml = strip_tags($htmlContent, '<p><br><strong><em><a><ul><ol><li><h1><h2><h3><h4><h5><h6>');
        
        // Simple Gutenberg block format
        $gutenbergContent = '<!-- wp:paragraph -->
' . $cleanHtml . '
<!-- /wp:paragraph -->';
        
        return $gutenbergContent;
    }
}
