<?php

namespace App\Console;

use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Console\Scheduling\Schedule;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        'App\Console\Commands\AddAddressToInscriptions',
        'App\Console\Commands\Reports\Create',
        'App\Console\Commands\Reports\Send',
        'App\Console\Commands\UnzipImportCentris',
        'App\Console\Commands\ImportDataCentris',
        'App\Console\Commands\CentrisFiles\ImportFirms',
        'App\Console\Commands\CentrisFiles\ImportOffices',
        'App\Console\Commands\CentrisFiles\ImportBrokers',
        'App\Console\Commands\CentrisFiles\ImportFixedvalues',
        'App\Console\Commands\CentrisFiles\ImportInscriptions',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsAddendas',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsCharacteristics',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsExpenses',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsRooms',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsUnitDetails',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsNotes',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsPhotos',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsRenovations',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsRevenuespaces',
        'App\Console\Commands\CentrisFiles\ImportMunicipalities',
        'App\Console\Commands\CentrisFiles\ImportNeighborhoods',
        'App\Console\Commands\CentrisFiles\ImportPropertiestypes',
        'App\Console\Commands\CentrisFiles\ImportSubtypecharacteristics',
        'App\Console\Commands\CentrisFiles\ImportTypecharacteristics',
        'App\Console\Commands\CentrisFiles\ImportRegions',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsOpenhouses',
        'App\Console\Commands\ImportBrokersToSites',
        'App\Console\Commands\SaveInscriptionsSold',
        'App\Console\Commands\Billing\CreateSitesDailyTransferts',
        'App\Console\Commands\Billing\CreateSitesMonthlyBills',
        'App\Console\Commands\ImportOldInscriptionsSold',
        'App\Console\Commands\SyncStageAndProd',
        'App\Console\Commands\ImportBlogPostToV2',
        'App\Console\Commands\ImportTestimonialsToV2',
        'App\Console\Commands\SiteDomainNameChange',
        'App\Console\Commands\CentrisFiles\ImportInscriptionsLinks',
        'App\Console\Commands\ImportGoogleMyBusinessReviews',
        'App\Console\Commands\PostFacebookInscriptions',
        'App\Console\Commands\UpdateEclosionNeighborhoodsCoordinates',
        'App\Console\Commands\ConvertNeighborhoodV2',
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')
        //          ->hourly();
    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        require base_path('routes/console.php');
    }
}
