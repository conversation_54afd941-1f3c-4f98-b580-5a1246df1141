<?php

namespace App\Models;

use App;
use Auth;
use EcloFiles;
use Illuminate\Support\Str;
use App\Models\Library\Base;
use Illuminate\Support\Facades\App as FacadesApp;
use App\Observers\SitesNeighborhoodV2Observer;
use Illuminate\Support\Facades\Request;
use Illuminate\Database\Eloquent\Builder;
use VanOns\Laraberg\Models\Gutenbergable;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class SitesNeighborhoodV2 extends Base
{
    use CrudTrait;
    use Gutenbergable;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'sites_neighborhoods_v2';
        
    protected $fillable = [
        'fk_site_id',
        'fk_eclosion_neighborhood_id',
        'fk_sites_neighborhood_id',
        'language',
        'name',
        'slug',
        'meta_title',
        'meta_description',
        'description',
        'content',
        'video_url',
        'header_image',
        'points_of_interest',
        'editableblocks',
        'geometry_type',
        'coordinates',
        'published',
        'no_index',
    ];

    protected $hidden = ['fk_sites_neighborhood_id', 'fk_site_id', 'fk_eclosion_neighborhood_id'];

    protected $appends = ['image_url','name_with_language'];

    protected $casts = [
        'points_of_interest' => 'array',
        'published' => 'boolean',
        'no_index' => 'boolean',
        'editableblocks' => 'array', 
    ];
    
    // format for thumbnails
    protected $thumbnailFormat = [
        [ 'width' => 1900, 'height' => null, 'prefix' => null, 'type' => 'resize', 'ratio' => true ]
    ];

    // Variables for medias management
    const DISK_NAME  = 'cdn';
    const FOLDER_IMG = 'neighborhoodsv2';
    const MEDIAS     = ['header_image'];

    /**
     * Static, Returns full formatted object for API of a single neighborhood.
     *
     * @param object $data, DB query result, join between SitesNeighborhood and EclosionNeighborhood
     * @return object
     */
    public static function formatForAPI($data)
    {
        $localized_name = 'name';
        $localized_text = 'text';
        $geoData = SitesNeighborhoodV2::select('name', 'slug', 'geometry_type', 'coordinates', 'id')->where('slug', '=', $data->slug)->get();
        $geoJSON = SitesNeighborhoodV2::getGeoJson($geoData);

        $relatedNeighborhoods = null;
        if ($data->fk_sites_neighborhood_id) {
            $relatedNeighborhoods = SitesNeighborhoodV2::find($data->fk_sites_neighborhood_id);
        }
        
        $slug = null;
        if ($relatedNeighborhoods) {
            $slug = $relatedNeighborhoods->slug;
        }

        //remove the fk_sites_neighborhood_id from the data
        unset($data->fk_sites_neighborhood_id);

        $data->translation_slug = $slug;
        // Data transform
        // PHOTOS
        // if ($data->photos !== null) {
        //     $data->photos = array_values((array) json_decode($data->photos));
        // }
        // POINTS OF INTEREST
        if ($data->points_of_interest !== null) {
            $data->points_of_interest = json_decode($data->points_of_interest);
            // Point of interests are a backpack field stored as a string. We need to loop over them to get correct language (hence $locale variable).
            foreach ($data->points_of_interest as $point) {
                if (!isset($point->$localized_name)) {
                    continue;
                }
                // store the only properties we're interested in
                $name = $point->$localized_name;
                // clear all properties
                foreach ($point as $key => $value) {
                    unset($point->$key);
                }
                //reassign
                $point->name = $name;
            }
        }
        
        if ($data->editableblocks !== null) {
            //change editableblocks to string
            $data->editableblocks = json_encode($data->editableblocks);
            $data->editableblocks = json_decode($data->editableblocks);
        }

        $data->description = html_entity_decode($data->description);
        $data->video_url = json_decode($data->video_url);
        if (!is_null($data->video_url)) {
            if ($data->video_url->provider == 'youtube')
            $data->video_embed  = 'https://www.youtube.com/embed/'.$data->video_url->id;
            elseif ($data->video_url->provider == 'vimeo')
            $data->video_embed  = 'https://player.vimeo.com/video/'.$data->video_url->id;
        }
        $data->averages_costs = json_decode($data->averages_costs);

        $data->geoJSON = $geoJSON;

        return $data;
    }
    /*
    |--------------------------------------------------------------------------
    | GETTERS
    |--------------------------------------------------------------------------
    */
    public function getImageUrlAttribute()
    {
        if (!empty($this->header_image)) {
            $url['fullsize'] = $this->header_image;
            $url['thumbnail'] = $this->header_image;
            return $url;
        }
        return null;
    }


    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function site()
    {
        return $this->belongsTo(Site::class, 'fk_site_id');
    }

    public function eclosion_neighborhood()
    {
        return $this->belongsTo(EclosionNeighborhood::class, 'fk_eclosion_neighborhood_id');
    }

    public function traduction()
    {
        return $this->belongsTo(SitesNeighborhoodV2::class, 'fk_sites_neighborhood_id');
    }
    /**
    * Get Image for neighborhood
    * @return App\Models\SitesNeighborhoodsImage
    */
    public function images()
    {
        return $this->hasMany('App\Models\SitesNeighborhoodsImageV2', 'fk_site_neighborhood_id');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */
    protected static function booted()
    {
        $request = request();
        
        //if API
        if (($request->API === true && $request->site_id != null)) {
            $site_id = $request->site_id;
            static::addGlobalScope('fk_site_id', function (Builder $builder) use ($site_id) {
                $builder->where('fk_site_id', $site_id);
            });
        } elseif ((backpack_auth()->check() && !backpack_user()->admin())) {
            //if site admin
            $site_ids[] = current_site_id();
            static::addGlobalScope('site_id', function (Builder $builder) use ($site_ids) {
                $builder->whereIn('fk_site_id', $site_ids);
            });
        } elseif ($request->ajax() && (!backpack_auth()->check() || !backpack_user()->admin())) {
            // if ajax search by admin panel (backpack ajax)
            $form = $request->all();
            $site_id = $form['form']['fk_site_id'];
            static::addGlobalScope('fk_site_id', function (Builder $builder) use ($site_id) {
                $builder->where('fk_site_id', $site_id);
            });
        }

        SitesNeighborhoodV2::observe(new SitesNeighborhoodV2Observer());
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    /**
     * Mutator for name and slug
     * @param String $value Name of the neighborhood
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    /**
     * Mutator for file upload header image
     * @param File $value Header image
     */
    public function setHeaderImageAttribute($value)
    {
        $fileManager = new EcloFiles();
        $attribute   = 'header_image';
        
        // If update, value will already be the url and not the file data info
        if (is_string($value) && strpos($value, "data:image/") === false) {
            return $this->attributes[$attribute] = $value;
        }

        // SET PATH
        $path = $this->_uploadPath .
            DIRECTORY_SEPARATOR .
            $this->site->uuid .
            DIRECTORY_SEPARATOR .
            self::FOLDER_IMG .
            DIRECTORY_SEPARATOR .
            $this->slug;

        $previousFile = $this->$attribute;

        // UPLOAD FILE
        $response = $fileManager->addDocument($attribute, $path, $value, $previousFile, $this->thumbnailFormat);

        // SAVE FILE URL TO DB
        $this->attributes[$attribute] = $response->get('filename');
    }

    public function getNameWithLanguageAttribute()
    {
        return $this->name . ' (' . strtoupper($this->language) . ')';
    }



    /**
     * Static function, returns a geoJSON Mapbox-compatible array
     *
     * @param  $values collection with objects
     * @return  array
     */
    public static function getGeoJson($values)
    {
        $geoJson = [
            "type" => "FeatureCollection",
            "features" => []
        ];
        
        foreach ($values as $neighborhood) {
            if ($neighborhood->coordinates && $neighborhood->geometry_type) {
                $geoJson["features"][] = [
                    "type" => "Feature",
                    "properties" => [
                        "id"           => $neighborhood->slug,
                        "description"  => $neighborhood->meta_description ?? html_entity_decode($neighborhood->description),
                        "name"         => $neighborhood->name,
                        "header_image" => $neighborhood->header_image,
                    ],
                    "geometry"   => [
                        "type"        => $neighborhood->geometry_type,
                        "coordinates" => json_decode($neighborhood->coordinates)
                    ]
                ];
            }
        }

        return (object) $geoJson;
    }

    // Custom buttons for CRUD
    public function crudViewButton()
    {
        $locale = $this->language;
        return '<a class="btn btn-xs btn-default" href="/' . $locale . '/neighborhoods/' . $this->slug . '" target="_blank" data-toggle="tooltip" title="Voir sur le site"><i class="fa fa-eye"></i> Voir</a>';
    }
}
