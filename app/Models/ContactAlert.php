<?php

namespace App\Models;

use App;
use Mail;
use GuzzleHttp\Client;
use App\Models\Library\Base;
use App\Mail\ContactAlertMailer;
use App\Mail\ContactConfirmationMailer;
use Illuminate\Support\Facades\Request;
use Illuminate\Database\Eloquent\Builder;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class ContactAlert extends Base
{
    use CrudTrait;

    /** @var Array Set fillable fields **/
    protected $fillable = [
        'firstname',
        'lastname',
        'phone',
        'phone_ext',
        'email_alert',
        'cities',
        'property_types',
        'language',
        'email_broker',
        'fk_site_id',
        'budget',
        'other',
        'IP',
    ];

    /**
     * The method used to send email.
     *
     * @param array $fields
     */
    public static function storeAndSendMail($fields)
    {
        $site = $fields['site'];
        // fields have already been validated by controller
        $contact = new ContactAlert([
            'firstname' => $fields['firstname'],
            'lastname' => $fields['lastname'],
            'phone' => $fields['phone'],
            'phone_ext' => isset($fields['phone_ext']) ? $fields['phone_ext'] : null,
            'email_alert' => $fields['email_alert'],
            'cities' => json_encode($fields['cities'], JSON_UNESCAPED_UNICODE),
            'property_types' => json_encode($fields['property_types'], JSON_UNESCAPED_UNICODE),
            'language' => $fields['language'],
            'email_broker' => $site->contact_email,
            'budget' => isset($fields['budget']) ? $fields['budget'] : null,
            'other' => isset($fields['other']) ? $fields['other'] : null,
            'fk_site_id' => $fields['site_id'],
            'IP' => $fields['ip']
        ]);

        $to = explode(',', $site->contact_email);
        $teammembers = $site->teammembers()->select('email')->where('mail_all', 1)->get();
        foreach ($teammembers as $teammember) {
            if (!in_array($teammember->email, $to)) {
                $to[] = $teammember->email;
            }
        }

        // logo path must be a string
        $logo_path = $site->{'logo_' . App::getLocale()};
        $logo_path = $logo_path == null ? '' : $logo_path;
        
        for ($i = 0; $i < count($to); $i++) {
            $to[$i] = preg_replace('/\s+/', '', trim($to[$i]));
        }

        // send email
        Mail::to($to)
                ->send(new ContactAlertMailer($contact, $logo_path));
        // confirmation
        Mail::to(preg_replace('/\s+/', '', trim($contact->email_alert)))
                ->send(new ContactConfirmationMailer([
                    'email_from' => $to[0],
                    'subject' => __('emails/contact.confirmation.alert.subject'),
                    'title' => __('emails/contact.confirmation.alert.title'),
                    'text' => __('emails/contact.confirmation.alert.text', ['name' => $contact->firstname.' '.$contact->lastname]),
                    'site_name' => $site->name,
                    'broker_email' => $site->contact_email,
                    'number_broker' =>  $site->contact_phone,
                    'mail_color' => $site->mail_color,
                ], $logo_path));
        // THEN save
        $contact->save();
        return [
            'code'    => 200,
            'message' => __('emails/contact.confirmation.alert.success'),
            'data'    => $contact
        ];
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    /**
     * @param DateTime $value
     */
    public function getCreatedAtAttribute($value)
    {
        $date = \Carbon\Carbon::parse($value);
        
        return $date->setTimezone(config('app.timezone_display'));
    }
    public function getLanguageAttribute($value)
    {
        if (Request::is('*/admin/*') && !empty($value)) {
            return __('admin.crud.country.' . $value);
        }
        return $value;
    }
    public function getPhoneAttribute($value)
    {
        if (Request::is('*/admin/*') && !empty($value)) {
            $phone = substr($value, 0, 3) . '-' . substr($value, 3, 3) . '-' . substr($value, 6);
            $phone .= (!empty($this->phone_ext)) ? ' Ext: ' . $this->phone_ext : '';
            return $phone;
        }
        return $value;
    }
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function site()
    {
        return $this->belongsTo('App\Models\Site', 'fk_site_id');
    }

    public function inscription()
    {
        return $this->belongsTo('App\Models\Inscription', 'fk_inscription_mls');
    }

    /*
    |--------------------------------------------------------------------------
    | OWNERSHIP MANAGEMENT
    |--------------------------------------------------------------------------
     */
    protected static function booted()
    {
        $request = request();
        //if API
        if (($request->ajax() && $request->site_id != null)) {
            $site_id = $request->site_id;
            static::addGlobalScope('fk_site_id', function (Builder $builder) use ($site_id) {
                $builder
                    ->where('fk_site_id', $site_id);
            });
        //if site admin
        } else if ((backpack_auth()->check() && !backpack_user()->admin())) {
            $site_ids[] = current_site_id();
            static::addGlobalScope('site_id', function (Builder $builder) use ($site_ids) {
                $builder
                    ->whereIn('fk_site_id', $site_ids);
            });
        // if ajax search by admin panel (backpack ajax)
        } else if ($request->ajax() && (!backpack_auth()->check() || !backpack_user()->admin())) {
            $form = $request->all();
            $site_id = $form['form']['fk_site_id'];
            static::addGlobalScope('fk_site_id', function (Builder $builder) use ($site_id) {
                $builder
                    ->where('fk_site_id', $site_id);
            });
        }
    }
}
