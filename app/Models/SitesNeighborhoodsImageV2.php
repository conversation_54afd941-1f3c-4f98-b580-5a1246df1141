<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use EcloFiles;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

use App\Models\Library\Base;
use App;

class SitesNeighborhoodsImageV2 extends Base
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'sites_neighborhoods_images_v2';
    protected $fillable = ['path', 'published', 'fk_site_neighborhood_id', 'title'];


    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function site_neighborhood(){
        return $this->belongsTo('App\Models\SitesNeighborhoodV2', 'fk_site_neighborhood_id');
    }
    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESORS
    |--------------------------------------------------------------------------
    */
    public function crudViewSiteNeighborhoodList()
    {
        return '<a style="float:right" href="'.url(App::getLocale().'/admin/sitesneighborhoodV2').'" data-toggle="tooltip"><i class="la la-angle-double-left"></i> Retour aux quartiers (v2)</a>';
    }
    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    public function setPathAttribute($value)
    {
        // LOAD FILE MANAGER
        $fileManager = new EcloFiles();

        // Crud field name
        $attribute = 'path';
        // If update, value will already be the url and not the file data info
        if (is_string($value) && strpos($value, "data:image/") === false) {
            return $this->attributes[$attribute] = $value;
        }

        // because multi upload are not only used for updated this images
        $idNeighborhood = (!empty(\Request::input('fk_id'))) ? \Request::input('fk_id') : \Request::input('fk_site_neighborhood_id');
        $neighborhood = SitesNeighborhoodV2::select('slug')->where('id', $idNeighborhood)->first();

        // SET PATH
        $path = $this->_uploadPath . DIRECTORY_SEPARATOR .
        backpack_user()->sites->first()->uuid . DIRECTORY_SEPARATOR .
        'neighborhoods' . DIRECTORY_SEPARATOR .
        $neighborhood->slug;

        // GET PREVIOUS FILE
        $previousFile = $this->$attribute;

        // UPLOAD FILE
        $response = $fileManager->addDocument($attribute, $path, $value, $previousFile);

        // SAVE FILE URL TO DB
        return $this->attributes[$attribute] = $response->get('filename');
    }

    public static function booted()
    {
        $request = request();
        static::deleted(function($obj) {
            $fileManager = new EcloFiles();
            $fileManager->delete($obj->path);
        });
        static::addGlobalScope('orderby', function (Builder $builder) {
            $builder->orderBy('lft');
        });
    }
}
