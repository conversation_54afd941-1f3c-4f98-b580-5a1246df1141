<?php

namespace App\Observers;

use App\Models\SitesNeighborhoodV2;
use App\Services\VarnishService;

class SitesNeighborhoodV2Observer
{
    /**
     * Handle the SitesNeighborhoodV2 "created" event.
     */
    public function created($sitesNeighborhoodV2)
    {
        self::clearCaches($sitesNeighborhoodV2);
    }

    /**
     * Handle the SitesNeighborhoodV2 "updated" event.
     */
    public function updated($sitesNeighborhoodV2)
    {
        self::clearCaches($sitesNeighborhoodV2);
    }

    /**
     * Handle the SitesNeighborhoodV2 "saved" event (covers both created and updated).
     */
    public function saved(SitesNeighborhoodV2 $neighborhood)
    {
        // Only handle translation linking if the translation field was changed
        if ($neighborhood->isDirty('fk_sites_neighborhood_id') || $neighborhood->wasRecentlyCreated) {
            $this->handleTranslationRelationship($neighborhood);
        }
    }

    /**
     * Handle both creating new relationships and removing old ones
     */
    private function handleTranslationRelationship(SitesNeighborhoodV2 $neighborhood)
    {
        $oldTranslationId = $neighborhood->getOriginal('fk_sites_neighborhood_id');
        $newTranslationId = $neighborhood->fk_sites_neighborhood_id;

        // Step 1: Remove old relationship if it existed
        if ($oldTranslationId && $oldTranslationId !== $newTranslationId) {
            $oldLinkedNeighborhood = SitesNeighborhoodV2::find($oldTranslationId);
            if ($oldLinkedNeighborhood && $oldLinkedNeighborhood->fk_sites_neighborhood_id == $neighborhood->id) {
                // Remove the reverse link
                $oldLinkedNeighborhood->updateQuietly([
                    'fk_sites_neighborhood_id' => null
                ]);
            }
        }

        // Step 2: Create new relationship if one is set
        if ($newTranslationId) {
            $newLinkedNeighborhood = SitesNeighborhoodV2::find($newTranslationId);
            
            if ($newLinkedNeighborhood && $newLinkedNeighborhood->fk_sites_neighborhood_id !== $neighborhood->id) {
                // Create the reverse link
                $newLinkedNeighborhood->updateQuietly([
                    'fk_sites_neighborhood_id' => $neighborhood->id
                ]);
            }
        }
    }

    /**
     * Handle the SitesNeighborhoodV2 "deleted" event.
     */
    public function deleted(SitesNeighborhoodV2 $neighborhood)
    {
        self::clearCaches($neighborhood);

        // Remove any reverse links when deleting
        if ($neighborhood->fk_sites_neighborhood_id) {
            $linkedNeighborhood = SitesNeighborhoodV2::find($neighborhood->fk_sites_neighborhood_id);
            if ($linkedNeighborhood) {
                $linkedNeighborhood->updateQuietly(['fk_sites_neighborhood_id' => null]);
            }
        }
        
        // Remove any neighborhood that points to this one
        SitesNeighborhoodV2::where('fk_sites_neighborhood_id', $neighborhood->id)
            ->update(['fk_sites_neighborhood_id' => null]);
    }

    private static function clearCaches($sites_neighborhood)
    {
        if (1 == config('app.syspark_varnish')) {
            $site = $sites_neighborhood->site;
            $domain_name = $site->domain_name;
            
            $endPoint = '/neighborhoods/map';
            VarnishService::clearVarnishForURL($endPoint, $domain_name);

            $endPoint = '/neighborhood/';
        }
    }
}
