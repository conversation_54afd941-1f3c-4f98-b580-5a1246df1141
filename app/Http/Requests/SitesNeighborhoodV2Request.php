<?php

namespace App\Http\Requests;

use App\Http\Requests\Request;
use App\Rules\Base64MimesCustom;
use Illuminate\Foundation\Http\FormRequest;

class SitesNeighborhoodV2Request extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fk_site_id' => 'required',
            'fk_eclosion_neighborhood_id' => 'required',
            'language' => 'required|in:fr,en',
            'name' => 'required|string|max:100',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'header_image' => new Base64MimesCustom(['png','jpg','jpeg','webp']),
            'published' => 'boolean',
            'no_index' => 'boolean',
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'fk_site_id' => __('validation.attributes.fk_site_id'),
            'fk_eclosion_neighborhood_id' => __('validation.attributes.fk_eclosion_neighborhood_id'),
            'language' => 'Langue',
            'name' => 'Nom',
            'meta_title' => 'Titre SEO',
            'meta_description' => 'Description SEO',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'language.in' => 'La langue doit être française (fr) ou anglaise (en).',
        ];
    }
}
