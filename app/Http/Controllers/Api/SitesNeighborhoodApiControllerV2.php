<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\EditableBlock;
use App\Models\SitesNeighborhoodV2;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\EclosionNeighborhood;

class SitesNeighborhoodApiControllerV2 extends Controller
{
    public function index(Request $request)
    {
        $data = SitesNeighborhoodV2::with(['neighborhood'])->where('language', App::getLocale())->orderBy('lft')->orderBy('name')->get();

        return response()->api($data, 200);
    }

    public function show(Request $request, $slug)
    {
        // Select only fields we need in both tables, using the query builder here allows us to make a more precise response object for the API
        $fields = [
            'sites_neighborhoods_v2.id',
            'sites_neighborhoods_v2.name',
            'sites_neighborhoods_v2.slug',
            'sites_neighborhoods_v2.language',
            'sites_neighborhoods_v2.lft',
            'sites_neighborhoods_v2.description',
            'sites_neighborhoods_v2.video_url',
            'sites_neighborhoods_v2.header_image',
            'sites_neighborhoods_v2.points_of_interest',
            'sites_neighborhoods_v2.editableblocks',
            'sites_neighborhoods_v2.meta_title',
            'sites_neighborhoods_v2.meta_description',
            'sites_neighborhoods_v2.fk_sites_neighborhood_id',
            'eclosion_neighborhoods.area',
            'eclosion_neighborhoods.population',
            'eclosion_neighborhoods.density',
            'eclosion_neighborhoods.perc_14',
            'eclosion_neighborhoods.perc_15_24',
            'eclosion_neighborhoods.perc_25_44',
            'eclosion_neighborhoods.perc_45_64',
            'eclosion_neighborhoods.perc_65',
            'eclosion_neighborhoods.perc_household_1',
            'eclosion_neighborhoods.perc_household_2',
            'eclosion_neighborhoods.perc_household_3',
            'eclosion_neighborhoods.perc_household_4',
            'eclosion_neighborhoods.perc_household_5',
            'eclosion_neighborhoods.perc_french',
            'eclosion_neighborhoods.perc_english',
            'eclosion_neighborhoods.perc_other',
            'eclosion_neighborhoods.averages_costs',
            'eclosion_neighborhoods.slug as blocks_slug',
        ];
        $data = SitesNeighborhoodV2::select($fields)
            ->where('sites_neighborhoods_v2.slug', $slug)
            ->where('sites_neighborhoods_v2.language', App::getLocale())
            ->with(['images' => function ($query) {
                $query->select(['path', 'fk_site_neighborhood_id']);
            }])
            ->join('eclosion_neighborhoods', 'sites_neighborhoods_v2.fk_eclosion_neighborhood_id', '=', 'eclosion_neighborhoods.id')
            ->first();
        if ($data === null) {
            return response()->api('', 404, 'Not found');
        }
        $blocks = EditableBlock::getById([
            'fk_identifier_id' => 'bloc-' . $data->blocks_slug
        ]);

        if(!$blocks) {
            $blocks = "";
        }
        $data->blocks = $blocks;

        // pagination
        $not_in = [$data->id];
        $data->prev = SitesNeighborhoodV2::select(
            'id',
            'name',
            'slug',
            'header_image',
        )->where('lft', '<=', $data->lft)->whereNotIn('id', $not_in)->where('language', App::getLocale())->orderBy('lft', 'desc')->orderBy('name')->first();
        if (!empty($data->prev)) {
            $not_in = array_merge($not_in, [$data->prev->id]);
        }
        $data->next = SitesNeighborhoodV2::select(
            'id',
            'name',
            'slug',
            'header_image',
        )->where('lft', '>=', $data->lft)->whereNotIn('id', $not_in)->where('language', App::getLocale())->orderBy('lft')->orderBy('name')->first();

        return response()->api(SitesNeighborhoodV2::formatForAPI($data), 200);
    }

    public function geoJSONAll()
    {
        $data = SitesNeighborhoodV2::select('name', 'slug', 'geometry_type', 'coordinates', 'description','meta_description', 'header_image')->where('language', App::getLocale())->orderBy('lft')->orderBy('name')->get();

        if ($data === null) {
            return response()->api('', 404, 'Not found');
        }

        return response()->api(SitesNeighborhoodV2::getGeoJson($data), 200);
    }

    public function geoJSONSingle($slug)
    {
        $data = SitesNeighborhoodV2::select('name', 'slug', 'geometry_type', 'coordinates', 'description', 'meta_description',)->where('slug', '=', $slug)->get();
        if ($data === null) {
            return response()->api('', 404, 'Not found');
        }

        return response()->api(SitesNeighborhoodV2::getGeoJson($data), 200);
    }

    public function geoJSONDefault($id)
    {
        $defaultneighborhood = EclosionNeighborhood::where('id', '=', $id)->first();
        
        if ($defaultneighborhood) {
            return response()->api($defaultneighborhood, 200);
        } else {
            return response()->api('', 404, 'Not found');
        }
    }
}
