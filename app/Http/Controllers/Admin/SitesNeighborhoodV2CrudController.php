<?php
// filepath: app/Http/Controllers/Admin/SitesNeighborhoodV2CrudController.php

namespace App\Http\Controllers\Admin;

use App\Services\VarnishService;
use Illuminate\Support\Facades\App;
use Backpack\CRUD\app\Library\Widget;
use App\Http\Requests\SitesNeighborhoodV2Request;
use App\Models\SitesNeighborhoodV2;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class SitesNeighborhoodV2CrudController extends BaseCrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation { saveReorder as traitReorder; }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel("App\Models\SitesNeighborhoodV2");
        CRUD::setRoute(App::getLocale() . "/admin/sitesneighborhoodv2");
        CRUD::setEntityNameStrings('quartier v2', 'quartiers v2');
        $this->crud->orderBy('lft');

        // Permission Check using BaseCrudController
        parent::permissionVerify("sitesneighborhoodv2");
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::addColumns([
            [
                'name' => 'language',
                'label' => 'Langue',
                'type' => 'text',
            ],
            [
                'name' => 'name',
                'label' => 'Nom',
                'type' => 'text',
            ],

            [
                'name' => 'site',
                'label' => 'Site',
                'type' => 'relationship',
                'attribute' => 'domain_name',
            ],
            [
                'name' => 'eclosion_neighborhood',
                'label' => 'Quartier Éclosion',
                'type' => 'relationship',
                'attribute' => 'name',
            ],
            [
                'name' => 'published',
                'label' => 'Publié',
                'type' => 'boolean',
            ],
        ]);

        CRUD::addFilter([
            'type' => 'dropdown',
            'name' => 'language',
            'label' => 'Langue'
        ], [
            'fr' => 'Français',
            'en' => 'English'
        ], function($value) {
            CRUD::addClause('where', 'language', $value);
        });

        CRUD::addFilter([
            'type' => 'dropdown',
            'name' => 'published',
            'label' => 'Statut'
        ], [
            1 => 'Publié',
            0 => 'Brouillon'
        ], function($value) {
            CRUD::addClause('where', 'published', $value);
        });

        $this->crud->addButtonFromView('top', 'reorder', 'reorder_fr', 'end');
        $this->crud->addButtonFromView('top', 'reorder_en', 'reorder_en', 'end');
    }

    /**
     * Define what happens when the Create operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(SitesNeighborhoodV2Request::class);

        $this->setupFields();
    }

    /**
     * Define what happens when the Update operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    // protected function setupReorderOperation()
    // {
    //     $this->crud->set('reorder.label', 'name');
    //     $this->crud->set('reorder.max_level', 1);
    // }

    // public function saveReorder()
    // {
    //     if (1 == config('app.syspark_varnish')) {
    //         $domain_name = current_site_filter()->domain_name;
    //         $endPoint = '/neighborhoods/map';
    //         VarnishService::clearVarnishForURL($endPoint, $domain_name);
    //     }
    //     // call the method in the trait
    //     return $this->traitReorder();
    // }

    protected function setupReorderOperation()
    {
        $this->crud->set('reorder.label', 'name');
        $this->crud->set('reorder.max_level', 1);
        
        // Get language from URL parameter, default to 'fr'
        $language = request()->get('lang', 'fr');
        
        // Filter by language
        $this->crud->addClause('where', 'language', $language);
    }

    public function saveReorder()
    {
        // Get language from request
        $language = request()->get('lang', 'fr');
        
        // Add the same language filter for saving
        $this->crud->addClause('where', 'language', $language);
        
        if (1 == config('app.syspark_varnish')) {
            $domain_name = current_site_filter()->domain_name;
            $endPoint = '/neighborhoods/map';
            VarnishService::clearVarnishForURL($endPoint, $domain_name);
        }
        
        // call the method in the trait
        return $this->traitReorder();
    }

    /**
    * Reorder French neighborhoods
    */
    public function reorderFr()
    {
        return redirect($this->crud->route . '/reorder?lang=fr');
    }

    /**
     * Reorder English neighborhoods
     */
    public function reorderEn()
    {
        return redirect($this->crud->route . '/reorder?lang=en');
    }

    /**
     * Setup fields for create and update operations
     */
    private function setupFields()
    {
        // These fields are shared by all roles, and merged with roles-depending-fields.
        $this->crud->addFields([
            [
                'label' => 'Site',
                'type'  => 'hidden',
                'name'  => 'fk_site_id',
                'value' => current_site_id()
            ],
            [
                'name'      => 'fk_eclosion_neighborhood_id',
                'label'     => __('admin.labels.neighborhood'),
                'type'      => 'select2',
                'entity'    => 'eclosion_neighborhood',
                'attribute' => 'name',
                'tab'       => __('admin.tabs.common-contents'),
                'model'     => "App\Models\EclosionNeighborhood",
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6'
                ]
            ],
            [
                'name'      => 'fk_sites_neighborhood_id',
                'label'     => 'Quartier associé',
                'type'      => 'relationship',
                'attribute' => 'name_with_language',
                'entity'    => 'traduction',
                'model'     => "App\Models\SitesNeighborhoodV2",
                'tab'       => __('admin.tabs.common-contents'),
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6'
                ]
            ],
            [
                'name' => 'language',
                'label' => 'Langue',
                'type' => 'select_from_array',
                'options' => ['fr' => 'Français', 'en' => 'English'],
                'allows_null' => false,
                'tab' => __('admin.tabs.common-contents'),
            ],
            [
                'name'       => 'name',
                'label'      => __('admin.labels.name'),
                'type'       => 'text',
                'tab'        => __('admin.tabs.common-contents'),
                'attributes' => ['placeholder' => 'Si vous laissez ce champ vide, il sera automatiquement défini par le nom du quartier associé'],
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6'
                ]
            ],
            [
                'name'              => 'slug',
                'label'             => 'Slug',
                'type'              => 'text',
                'tab'               => __('admin.tabs.common-contents'),
                'attributes'        => ['disabled' =>'disabled', 'placeholder' => 'Automatiquement défini par le nom du quartier'],
                'hint'              => "Permet de construire l'url vers la page quartier. Ce champ se génère automatiquement.",
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6 info-help'
                ]
            ],
            [
                'name'  => 'meta_title',
                'label' => __('admin.labels.meta_title'),
                'type'  => 'text',
                'tab'   => 'SEO',
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6'
                ]
            ],
            [
                'name'  => 'meta_description',
                'label' => __('admin.labels.meta_desc'),
                'type'  => 'textarea',
                'tab'   => 'SEO',
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6'
                ]
            ],
            [
                'name'  => 'video_url',
                'label' => __('admin.labels.video'),
                'youtube_api_key' => config('eclosion.youtube_api_key'),
                'type'  => 'video',
                'tab'   => __('admin.tabs.common-contents'),
                'hint'  => "Renseigner ici l'url de la vidéo que vous souhaitez ajouter au quartier sur votre site web. Veillez à utiliser l'url de la vidéo présente dans la barre de navigation de votre navigateur et non l'url de partage fournie par Youtube et Viméo.
                Exemple: https: //www.youtube.com/watch?v=tFhQ6jgkcAM",
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-6 info-help'
                ]
            ],
            [
                'name'  => "description",
                'label' => __('admin.labels.description'),
                'tab'   => __('admin.tabs.common-contents'),
                'type'  => "laraberg"
            ],
            [
                'name'              => 'header_image',
                'label'             => __('admin.labels.header_image') . config('app.php_max_upload_size'),
                'type'              => 'image',
                'upload'            => true,
                'crop'              => false,
                'aspect_ratio'      => 1,
                'tab'               => __('admin.tabs.common-contents'),
                'hint'              => "L'image d'entête ne doit pas excéder 16 megaoctets. \n" . __('admin.common.image_format_hint', ['values' => 'png, jpg, jpeg, webp']),
                'wrapperAttributes' => [
                    'class' => 'form-group col-md-12 info-help image'
                ]
            ],
            [
                'name'  => 'points_of_interest',
                'label' => __('admin.labels.points_of_interest'),
                'type'  => 'repeatable',
                'tab'   => __('admin.tabs.common-contents'),
                'new_item_label' => __('admin.labels.points_of_interest_button'),
                'fields'         => [
                    [
                        'name' => 'name',
                        'type' => 'textarea',
                        'label' => 'Nom',
                        'wrapper' => ['class' => 'form-group col-md-6']
                    ],
                ],
            ],
            [
                'label' => 'Images',
                'type' => 'upload_multiple_dropzone', // the name for vue
                'name' => 'path', // the name of field in db
                'attributes' => [ // list of database fields for adding
                    'path_img' => 'path', // REQUIRED
                    'name_img' => 'title', // REQUIRED
                    'fk_name' => 'fk_site_neighborhood_id', // REQUIRED
                    'reorder' => 'lft' // the name of field in db (optionnal)
                ],
                'datas' => [
                    'model'      => 'App\Models\SitesNeighborhoodV2',
                    'entity'     => 'images',
                    'fk_id'      => $this->crud->actionIs('edit') ? $this->crud->getCurrentEntry()->id : false,
                    'model_name' => 'SitesNeighborhoodsImageV2',
                    'rename'     => true // If you want rename option (optionnal)
                ],
                'tab' => 'Média'
            ]
        ]);
        if (backpack_user()->hasPermissionTo('content_neighborhood')) {
            $iconset = [
                'iconClass'    => 'custom',
                'iconClassFix' => 'icon-',
                'icons'        => ['', 'more-info', 'round-dollar', 'fav-property', 'shower', 'acheter-vendre', 'affaire', 'argent', 'arts', 'augmentation', 'auto', 'bibliotheque', 'ce-qu-il-disent', 'couple', 'education', 'lac-piscine', 'medaille', 'nightlife', 'nourriture', 'parc-amusement', 'parc', 'personnes-agees', 'population', 'sante', 'statistiques', 'sourire', 'terrasse', 'thumbs-up', 'train', 'velo', 'logo-youtube', 'website', 'search', 'close', 'bus', 'coeur', 'famille', 'foret', 'sport', 'ville', 'logo-facebook', 'logo-googleplus', 'logo-instagram', 'logo-linkedin', 'logo-mail', 'logo-twitter', '360', 'alerte', 'arrow-externe', 'arrow-left', 'arrow-right', 'calendar', 'check', 'checked', 'dropdown-menu', 'favorite', 'alerte-1', 'eval-1', 'mail-outline', 'mail', 'map-minus', 'map-plus', 'mobile', 'phone-outline', 'phone', 'pin-outline', 'pin', 'play', 'preference', 'print-outline', 'print', 'refresh', 'user', 'area', 'bed', 'garage', 'alert-1', 'eval-12', 'sink']
            ];
            $this->crud->addFields([
                [
                    'name'   => 'editableblocks',
                    'label'  => __('admin.labels.editable-blocks'),
                    'type'   => 'repeatable',
                    'tab'    => __('admin.tabs.editable-blocks'),
                    'fields' => [
                        [
                            'name'    => 'icon',
                            'label'   => 'Icone',
                            'type'    => 'icon_picker_custom',
                            'iconset' => $iconset,
                            'wrapper' => ['class' => 'form-group col-md-2']
                        ],
                        [
                            'name' => 'title',
                            'type'    => 'text',
                            'label'   => __('admin.labels.title'),
                            'wrapper' => ['class' => 'form-group col-md-10']
                        ],
                        [
                            'name' => 'text',
                            'label' => __('admin.labels.text'),
                            'type' => 'summernote',
                            'options' =>  [
                                'toolbar' => [
                                    ['style', ['style']],
                                    ['font', ['bold', 'italic', 'underline', 'strikethrough', 'clear']],
                                    ['color', ['color']],
                                    ['para', ['ul', 'ol', 'paragraph']],
                                    ['table', ['table']],
                                    ['insert', ['link']],
                                    ['view', ['fullscreen', 'codeview', 'help']],
                                ]
                            ],
                        ],
                    ],
                    'init_rows'      => 0,
                    'max_rows'       => 5,
                    'reorder'        => true,
                    'new_item_label' => __('admin.labels.editable-blocks'),
                    'tab'            => __('admin.tabs.common-contents'),
                ],
            ]);
        }

        if (backpack_user()->isImpersonating() === true) {

            Widget::add()->type('script')->content('js/admin/forms/geojson.js');



            $this->crud->addFields([
                [
                    'name'        => 'geometry_type',
                    'label'       => "Type de polygone (carte)",
                    'type'        => 'select_from_array',
                    'options'     => ['Polygon' => 'Polygon', 'MultiPolygon' => 'MultiPolygon'],
                    'allows_null' => false,
                    'hint'        => "Ce champ est réservé au développeur exclusivement.
                    IMPORTANT DE NE PAS Y TOUCHER SOUS PEINE DE DYSFONCTIONNEMENT DE VOTRE SITE WEB",
                    'tab'               => 'GeoJson',
                    'wrapperAttributes' => [
                        'class' => 'form-group col-md-12 info-help impersonating'
                    ]
                ],
                [
                    'name'  => 'coordinates',
                    'label' => 'Coordonnées (carte)',
                    'type'  => 'textarea',
                    'hint'  => "Ce champ est réservé au développeur exclusivement.
                    IMPORTANT DE NE PAS Y TOUCHER SOUS PEINE DE DYSFONCTIONNEMENT DE VOTRE SITE WEB",
                    'tab'               => 'GeoJson',
                    'wrapperAttributes' => [
                        'class' => 'form-group col-md-12 info-help impersonating'
                    ],
                    'attributes' => [
                        'rows' => 20
                    ]
                ]
            ]);
        }
    }
}